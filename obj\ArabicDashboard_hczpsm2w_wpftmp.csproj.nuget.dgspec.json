{"format": 1, "restore": {"E:\\ArabicDashboard_backup\\ArabicDashboard.csproj": {}}, "projects": {"E:\\ArabicDashboard_backup\\ArabicDashboard.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\ArabicDashboard_backup\\ArabicDashboard.csproj", "projectName": "ArabicDashboard", "projectPath": "E:\\ArabicDashboard_backup\\ArabicDashboard.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\ArabicDashboard_backup\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"BouncyCastle.NetCore": {"target": "Package", "version": "[2.2.1, )"}, "DevExpress.WPF.ProjectTemplates": {"target": "Package", "version": "[25.1.1-alpha, )"}, "DevExpress.Wpf.Charts": {"target": "Package", "version": "[24.1.7, )"}, "DevExpress.Wpf.Grid": {"target": "Package", "version": "[24.1.7, )"}, "DevExpress.Wpf.Themes.All": {"target": "Package", "version": "[24.1.7, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.2210.55, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "iTextSharp.LGPLv2.Core": {"target": "Package", "version": "[3.7.4, )"}, "itext7": {"target": "Package", "version": "[9.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}