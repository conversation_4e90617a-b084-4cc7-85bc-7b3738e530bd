﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArabicDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddCommitmentsTableOnly : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_InquiryCalculations_ClientName",
                table: "InquiryCalculations");

            migrationBuilder.DropColumn(
                name: "ClientName",
                table: "InquiryCalculations");

            migrationBuilder.RenameColumn(
                name: "SponsorType",
                table: "InquiryCalculations",
                newName: "WorkerType");

            migrationBuilder.RenameColumn(
                name: "ResidenceExpiryDate",
                table: "InquiryCalculations",
                newName: "ExpiryDate");

            migrationBuilder.RenameColumn(
                name: "MedicalInsuranceFees",
                table: "InquiryCalculations",
                newName: "WorkPermitFees");

            migrationBuilder.RenameColumn(
                name: "IsSponsorshipTransfer",
                table: "InquiryCalculations",
                newName: "ServiceType");

            migrationBuilder.RenameColumn(
                name: "IsEligibleForWorkPermit",
                table: "InquiryCalculations",
                newName: "RequiresManualRenewal");

            migrationBuilder.RenameColumn(
                name: "EmploymentFees",
                table: "InquiryCalculations",
                newName: "TransferFees");

            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "Invoices",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "Invoices",
                type: "TEXT",
                maxLength: 1000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 1000,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientAddress",
                table: "Invoices",
                type: "TEXT",
                maxLength: 300,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ClientIdNumber",
                table: "Invoices",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "Invoices",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "ExportedDate",
                table: "Invoices",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InvoiceType",
                table: "Invoices",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsElectronic",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "PrintCount",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "PrintedDate",
                table: "Invoices",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "QRCode",
                table: "Invoices",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "TaxAmount",
                table: "Invoices",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "TaxNumber",
                table: "Invoices",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "LaborOfficeFees",
                table: "InquiryCalculations",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "MedicalInsurance",
                table: "InquiryCalculations",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SaudiEmployment",
                table: "InquiryCalculations",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "WorkerName",
                table: "InquiryCalculations",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "Archives",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ArchiveName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ArchiveDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ArchiveType = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    DataJson = table.Column<string>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    InvoicesCount = table.Column<int>(type: "INTEGER", nullable: false),
                    MoneyTransfersCount = table.Column<int>(type: "INTEGER", nullable: false),
                    SharedAccountsCount = table.Column<int>(type: "INTEGER", nullable: false),
                    SimpleTransactionsCount = table.Column<int>(type: "INTEGER", nullable: false),
                    AgentTransactionsCount = table.Column<int>(type: "INTEGER", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Archives", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Commitments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CommitmentName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CommitmentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CommitmentCategory = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IsNotificationEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PaymentDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Commitments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Tasks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    Priority = table.Column<int>(type: "INTEGER", nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    StartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    AssignedTo = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    ClientName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    TransactionType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    TaskType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IsNotificationEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    LastNotificationDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Progress = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tasks", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_ExpiryDate",
                table: "InquiryCalculations",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_ServiceType",
                table: "InquiryCalculations",
                column: "ServiceType");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_WorkerName",
                table: "InquiryCalculations",
                column: "WorkerName");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_WorkerType",
                table: "InquiryCalculations",
                column: "WorkerType");

            migrationBuilder.CreateIndex(
                name: "IX_Archives_ArchiveDate",
                table: "Archives",
                column: "ArchiveDate");

            migrationBuilder.CreateIndex(
                name: "IX_Archives_ArchiveType",
                table: "Archives",
                column: "ArchiveType");

            migrationBuilder.CreateIndex(
                name: "IX_Archives_IsActive",
                table: "Archives",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_CommitmentCategory",
                table: "Commitments",
                column: "CommitmentCategory");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_CommitmentType",
                table: "Commitments",
                column: "CommitmentType");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_DueDate",
                table: "Commitments",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_IsActive",
                table: "Commitments",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Commitments_Status",
                table: "Commitments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_AssignedTo",
                table: "Tasks",
                column: "AssignedTo");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_DueDate",
                table: "Tasks",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Priority",
                table: "Tasks",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Status",
                table: "Tasks",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Archives");

            migrationBuilder.DropTable(
                name: "Commitments");

            migrationBuilder.DropTable(
                name: "Tasks");

            migrationBuilder.DropIndex(
                name: "IX_InquiryCalculations_ExpiryDate",
                table: "InquiryCalculations");

            migrationBuilder.DropIndex(
                name: "IX_InquiryCalculations_ServiceType",
                table: "InquiryCalculations");

            migrationBuilder.DropIndex(
                name: "IX_InquiryCalculations_WorkerName",
                table: "InquiryCalculations");

            migrationBuilder.DropIndex(
                name: "IX_InquiryCalculations_WorkerType",
                table: "InquiryCalculations");

            migrationBuilder.DropColumn(
                name: "ClientAddress",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "ClientIdNumber",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "ExportedDate",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "InvoiceType",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "IsElectronic",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "PrintCount",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "PrintedDate",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "QRCode",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "TaxAmount",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "TaxNumber",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "LaborOfficeFees",
                table: "InquiryCalculations");

            migrationBuilder.DropColumn(
                name: "MedicalInsurance",
                table: "InquiryCalculations");

            migrationBuilder.DropColumn(
                name: "SaudiEmployment",
                table: "InquiryCalculations");

            migrationBuilder.DropColumn(
                name: "WorkerName",
                table: "InquiryCalculations");

            migrationBuilder.RenameColumn(
                name: "WorkerType",
                table: "InquiryCalculations",
                newName: "SponsorType");

            migrationBuilder.RenameColumn(
                name: "WorkPermitFees",
                table: "InquiryCalculations",
                newName: "MedicalInsuranceFees");

            migrationBuilder.RenameColumn(
                name: "TransferFees",
                table: "InquiryCalculations",
                newName: "EmploymentFees");

            migrationBuilder.RenameColumn(
                name: "ServiceType",
                table: "InquiryCalculations",
                newName: "IsSponsorshipTransfer");

            migrationBuilder.RenameColumn(
                name: "RequiresManualRenewal",
                table: "InquiryCalculations",
                newName: "IsEligibleForWorkPermit");

            migrationBuilder.RenameColumn(
                name: "ExpiryDate",
                table: "InquiryCalculations",
                newName: "ResidenceExpiryDate");

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Notes",
                table: "Invoices",
                type: "TEXT",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 1000);

            migrationBuilder.AddColumn<string>(
                name: "ClientName",
                table: "InquiryCalculations",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_ClientName",
                table: "InquiryCalculations",
                column: "ClientName");
        }
    }
}
