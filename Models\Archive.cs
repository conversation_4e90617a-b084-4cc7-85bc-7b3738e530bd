using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ArabicDashboard.Data;

namespace ArabicDashboard.Models
{
    public class Archive
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string ArchiveName { get; set; } = string.Empty;

        [Required]
        public DateTime ArchiveDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(20)]
        public string ArchiveType { get; set; } = string.Empty; // Monthly, Manual

        [Required]
        public string DataJson { get; set; } = string.Empty; // JSON data

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public int InvoicesCount { get; set; }
        public int MoneyTransfersCount { get; set; }
        public int SharedAccountsCount { get; set; }
        public int SimpleTransactionsCount { get; set; }
        public int AgentTransactionsCount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }

        [StringLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        // خصائص محسوبة
        public string FormattedArchiveDate => ArchiveDate.ToString("yyyy/MM/dd HH:mm");
        public string FormattedTotalAmount => $"{TotalAmount:N2} ريال";
        public int TotalRecords => InvoicesCount + MoneyTransfersCount + SharedAccountsCount + 
                                  SimpleTransactionsCount + AgentTransactionsCount;
    }

    public class ArchiveData
    {
        public List<Invoice> Invoices { get; set; } = new List<Invoice>();
        public List<MoneyTransfer> MoneyTransfers { get; set; } = new List<MoneyTransfer>();
        public List<SharedAccount> SharedAccounts { get; set; } = new List<SharedAccount>();
        public List<SimpleTransactionEntity> SimpleTransactions { get; set; } = new List<SimpleTransactionEntity>();
        public List<AgentTransactionEntity> AgentTransactions { get; set; } = new List<AgentTransactionEntity>();
        public Dictionary<string, object> Statistics { get; set; } = new Dictionary<string, object>();
    }

    public class ArchiveSearchFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? ArchiveType { get; set; }
        public string? SearchText { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }
}
