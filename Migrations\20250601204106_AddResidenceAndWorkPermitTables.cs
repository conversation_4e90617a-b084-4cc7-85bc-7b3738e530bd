﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArabicDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddResidenceAndWorkPermitTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ResidenceInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ResidenceNumber = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    HolderName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Nationality = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IssueDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    ResidenceType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    SponsorName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    SponsorId = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    LastUpdated = table.Column<DateTime>(type: "TEXT", nullable: false),
                    DataSource = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResidenceInfos", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WorkPermitInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ResidenceNumber = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    WorkPermitNumber = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    IssueDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    EmployerName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    JobType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    LastUpdated = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkPermitInfos", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ResidenceInfos_ExpiryDate",
                table: "ResidenceInfos",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_ResidenceInfos_HolderName",
                table: "ResidenceInfos",
                column: "HolderName");

            migrationBuilder.CreateIndex(
                name: "IX_ResidenceInfos_ResidenceNumber",
                table: "ResidenceInfos",
                column: "ResidenceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ResidenceInfos_Status",
                table: "ResidenceInfos",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPermitInfos_ResidenceNumber",
                table: "WorkPermitInfos",
                column: "ResidenceNumber");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPermitInfos_Status",
                table: "WorkPermitInfos",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPermitInfos_WorkPermitNumber",
                table: "WorkPermitInfos",
                column: "WorkPermitNumber");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ResidenceInfos");

            migrationBuilder.DropTable(
                name: "WorkPermitInfos");
        }
    }
}
