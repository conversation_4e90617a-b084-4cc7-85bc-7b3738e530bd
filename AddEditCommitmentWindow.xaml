<dx:ThemedWindow x:Class="ArabicDashboard.AddEditCommitmentWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
                 xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
                 Title="📝 إدارة الالتزام"
                 Height="600" Width="500"
                 WindowStartupLocation="CenterScreen"
                 FlowDirection="RightToLeft"
                 FontFamily="Segoe UI"
                 FontSize="14"
                 ResizeMode="NoResize">

    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📝" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <StackPanel>
                    <TextBlock x:Name="TitleTextBlock" Text="إضافة التزام جديد" FontSize="18" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="أدخل تفاصيل الالتزام مع تحديد تاريخ الاستحقاق" FontSize="12" Foreground="#6B7280"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج الإدخال -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- اسم الالتزام -->
                <TextBlock Text="📋 اسم الالتزام:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:TextEdit x:Name="CommitmentNameTextBox" 
                            Height="40"
                            NullText="أدخل اسم الالتزام..."
                            FontSize="14"
                            Margin="0,0,0,15"/>

                <!-- نوع الالتزام -->
                <TextBlock Text="🏷️ نوع الالتزام:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:ComboBoxEdit x:Name="CommitmentTypeCombo" 
                                Height="40"
                                IsTextEditable="True"
                                NullText="اختر أو أدخل نوع الالتزام..."
                                FontSize="14"
                                Margin="0,0,0,15">
                    <dxe:ComboBoxEditItem Content="فاتورة كهرباء"/>
                    <dxe:ComboBoxEditItem Content="فاتورة مياه"/>
                    <dxe:ComboBoxEditItem Content="فاتورة هاتف"/>
                    <dxe:ComboBoxEditItem Content="إيجار"/>
                    <dxe:ComboBoxEditItem Content="قسط سيارة"/>
                    <dxe:ComboBoxEditItem Content="قسط بنكي"/>
                    <dxe:ComboBoxEditItem Content="تأمين"/>
                    <dxe:ComboBoxEditItem Content="رسوم حكومية"/>
                    <dxe:ComboBoxEditItem Content="اشتراك"/>
                    <dxe:ComboBoxEditItem Content="أخرى"/>
                </dxe:ComboBoxEdit>

                <!-- تاريخ الاستحقاق -->
                <TextBlock Text="📅 تاريخ الاستحقاق:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:DateEdit x:Name="DueDatePicker" 
                            Height="40"
                            FontSize="14"
                            Margin="0,0,0,15"/>

                <!-- تصنيف الالتزام -->
                <TextBlock Text="🏢 تصنيف الالتزام:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:ComboBoxEdit x:Name="CategoryCombo" 
                                Height="40"
                                FontSize="14"
                                SelectedIndex="0"
                                Margin="0,0,0,15">
                    <dxe:ComboBoxEditItem Content="شخصي"/>
                    <dxe:ComboBoxEditItem Content="معاملة"/>
                </dxe:ComboBoxEdit>

                <!-- المبلغ -->
                <TextBlock Text="💰 المبلغ (ريال):" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:SpinEdit x:Name="AmountSpinEdit" 
                            Height="40"
                            FontSize="14"
                            MinValue="0"
                            MaxValue="999999999"
                            Increment="1"
                            Margin="0,0,0,15"/>

                <!-- الوصف -->
                <TextBlock Text="📝 الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:TextEdit x:Name="DescriptionTextBox" 
                            Height="40"
                            NullText="وصف مختصر للالتزام..."
                            FontSize="14"
                            Margin="0,0,0,15"/>

                <!-- الملاحظات -->
                <TextBlock Text="📄 الملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                <dxe:MemoEdit x:Name="NotesTextBox" 
                            Height="80"
                            NullText="ملاحظات إضافية..."
                            FontSize="14"
                            Margin="0,0,0,15"/>

                <!-- تفعيل التنبيهات -->
                <dxe:CheckEdit x:Name="NotificationCheckBox" 
                             Content="🔔 تفعيل التنبيه قبل 15 يوم من الاستحقاق"
                             IsChecked="True"
                             FontSize="14"
                             Margin="0,5,0,15"/>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <dx:SimpleButton x:Name="SaveBtn" 
                               Content="💾 حفظ" 
                               Click="SaveBtn_Click"
                               Margin="0,0,10,0"
                               Padding="20,10"
                               Background="#10B981"
                               Foreground="White"
                               BorderThickness="0"
                               FontWeight="Bold"/>
                
                <dx:SimpleButton x:Name="CancelBtn" 
                               Content="❌ إلغاء" 
                               Click="CancelBtn_Click"
                               Padding="20,10"
                               Background="#6B7280"
                               Foreground="White"
                               BorderThickness="0"/>
            </StackPanel>
        </Border>
    </Grid>
</dx:ThemedWindow>
