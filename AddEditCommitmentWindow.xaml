<dx:ThemedWindow x:Class="ArabicDashboard.AddEditCommitmentWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
                 xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
                 xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
                 Title="📝 إدارة الالتزام"
                 Height="650" Width="600"
                 WindowStartupLocation="CenterScreen"
                 FlowDirection="RightToLeft"
                 FontFamily="Segoe UI"
                 FontSize="14"
                 ResizeMode="NoResize">

    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📝" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <StackPanel>
                    <TextBlock x:Name="TitleTextBlock" Text="إضافة التزام جديد" FontSize="18" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="أدخل تفاصيل الالتزام مع تحديد تاريخ الاستحقاق والتنبيهات" FontSize="12" Foreground="#6B7280"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج الإدخال -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <dxlc:LayoutControl Orientation="Vertical" ItemSpace="15">
                
                <!-- اسم الالتزام -->
                <dxlc:LayoutItem Label="📋 اسم الالتزام:" LabelPosition="Top">
                    <dxe:TextEdit x:Name="CommitmentNameTextBox" 
                                Height="40"
                                NullText="أدخل اسم الالتزام..."
                                FontSize="14"/>
                </dxlc:LayoutItem>

                <!-- نوع الالتزام -->
                <dxlc:LayoutItem Label="🏷️ نوع الالتزام:" LabelPosition="Top">
                    <dxe:ComboBoxEdit x:Name="CommitmentTypeCombo" 
                                    Height="40"
                                    IsTextEditable="True"
                                    NullText="اختر أو أدخل نوع الالتزام..."
                                    FontSize="14">
                        <dxe:ComboBoxEditItem Content="فاتورة كهرباء"/>
                        <dxe:ComboBoxEditItem Content="فاتورة مياه"/>
                        <dxe:ComboBoxEditItem Content="فاتورة هاتف"/>
                        <dxe:ComboBoxEditItem Content="إيجار"/>
                        <dxe:ComboBoxEditItem Content="قسط سيارة"/>
                        <dxe:ComboBoxEditItem Content="قسط بنكي"/>
                        <dxe:ComboBoxEditItem Content="تأمين"/>
                        <dxe:ComboBoxEditItem Content="رسوم حكومية"/>
                        <dxe:ComboBoxEditItem Content="اشتراك"/>
                        <dxe:ComboBoxEditItem Content="أخرى"/>
                    </dxe:ComboBoxEdit>
                </dxlc:LayoutItem>

                <!-- تاريخ الاستحقاق -->
                <dxlc:LayoutItem Label="📅 تاريخ الاستحقاق:" LabelPosition="Top">
                    <dxe:DateEdit x:Name="DueDatePicker" 
                                Height="40"
                                FontSize="14"
                                Mask="yyyy/MM/dd"
                                MaskType="DateTime"/>
                </dxlc:LayoutItem>

                <!-- تصنيف الالتزام -->
                <dxlc:LayoutItem Label="🏢 تصنيف الالتزام:" LabelPosition="Top">
                    <dxe:ComboBoxEdit x:Name="CategoryCombo" 
                                    Height="40"
                                    FontSize="14"
                                    SelectedIndex="0">
                        <dxe:ComboBoxEditItem Content="شخصي"/>
                        <dxe:ComboBoxEditItem Content="معاملة"/>
                    </dxe:ComboBoxEdit>
                </dxlc:LayoutItem>

                <!-- المبلغ -->
                <dxlc:LayoutItem Label="💰 المبلغ (ريال):" LabelPosition="Top">
                    <dxe:SpinEdit x:Name="AmountSpinEdit"
                                Height="40"
                                FontSize="14"
                                MinValue="0"
                                MaxValue="999999999"
                                Increment="1"
                                Mask="n2"
                                MaskType="Numeric"/>
                </dxlc:LayoutItem>

                <!-- الوصف -->
                <dxlc:LayoutItem Label="📝 الوصف:" LabelPosition="Top">
                    <dxe:TextEdit x:Name="DescriptionTextBox" 
                                Height="40"
                                NullText="وصف مختصر للالتزام..."
                                FontSize="14"/>
                </dxlc:LayoutItem>

                <!-- الملاحظات -->
                <dxlc:LayoutItem Label="📄 الملاحظات:" LabelPosition="Top">
                    <dxe:MemoEdit x:Name="NotesTextBox" 
                                Height="80"
                                NullText="ملاحظات إضافية..."
                                FontSize="14"/>
                </dxlc:LayoutItem>

                <!-- تفعيل التنبيهات -->
                <dxlc:LayoutItem Label="🔔 التنبيهات:" LabelPosition="Top">
                    <StackPanel Orientation="Horizontal">
                        <dxe:CheckEdit x:Name="NotificationCheckBox" 
                                     Content="تفعيل التنبيه قبل 15 يوم من الاستحقاق"
                                     IsChecked="True"
                                     FontSize="14"
                                     Margin="0,5"/>
                    </StackPanel>
                </dxlc:LayoutItem>

                <!-- معلومات إضافية -->
                <Border Background="#EFF6FF" BorderBrush="#DBEAFE" BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,10">
                    <StackPanel>
                        <TextBlock Text="💡 معلومات مفيدة:" FontWeight="Bold" FontSize="14" Foreground="#1E40AF" Margin="0,0,0,8"/>
                        <TextBlock Text="• سيتم تنبيهك قبل 15 يوم من تاريخ الاستحقاق" FontSize="12" Foreground="#374151" Margin="0,2"/>
                        <TextBlock Text="• يمكنك تعديل الالتزام في أي وقت من الجدول الرئيسي" FontSize="12" Foreground="#374151" Margin="0,2"/>
                        <TextBlock Text="• الالتزامات المتأخرة ستظهر باللون الأحمر" FontSize="12" Foreground="#374151" Margin="0,2"/>
                        <TextBlock Text="• يمكنك تسديد الالتزام مباشرة من قائمة السياق" FontSize="12" Foreground="#374151" Margin="0,2"/>
                    </StackPanel>
                </Border>

            </dxlc:LayoutControl>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <dx:SimpleButton x:Name="SaveBtn" 
                               Content="💾 حفظ" 
                               Click="SaveBtn_Click"
                               Margin="0,0,10,0"
                               Padding="20,10"
                               Background="#10B981"
                               Foreground="White"
                               BorderThickness="0"
                               CornerRadius="6"
                               FontWeight="Bold"/>
                
                <dx:SimpleButton x:Name="CancelBtn" 
                               Content="❌ إلغاء" 
                               Click="CancelBtn_Click"
                               Padding="20,10"
                               Background="#6B7280"
                               Foreground="White"
                               BorderThickness="0"
                               CornerRadius="6"/>
            </StackPanel>
        </Border>
    </Grid>
</dx:ThemedWindow>
