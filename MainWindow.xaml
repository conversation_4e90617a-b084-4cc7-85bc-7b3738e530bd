<Window x:Class="ArabicDashboard.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
        xmlns:dxc="http://schemas.devexpress.com/winfx/2008/xaml/charts"
        xmlns:local="clr-namespace:ArabicDashboard"
        Title="لوحة التحكم الرئيسية"
        Height="900" Width="1400"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial, Microsoft Sans Serif"
        FontSize="14"
        Background="#F8FAFC"
        dx:ThemeManager.ThemeName="Office2019Colorful">

    <Window.Resources>
        <!-- محول لتحديد الزر المختار -->
        <local:SelectedPageConverter x:Key="SelectedPageConverter"/>

        <!-- الألوان -->
        <LinearGradientBrush x:Key="PurpleCard" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#8B5CF6" Offset="0"/>
            <GradientStop Color="#A78BFA" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="GreenCard" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#34D399" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="BlueCard" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#3B82F6" Offset="0"/>
            <GradientStop Color="#60A5FA" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="OrangeCard" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F59E0B" Offset="0"/>
            <GradientStop Color="#FBBF24" Offset="1"/>
        </LinearGradientBrush>
        
        <SolidColorBrush x:Key="SidebarColor" Color="#2D3748"/>
        <SolidColorBrush x:Key="FooterColor" Color="#4299E1"/>
        
        <!-- أنماط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Margin" Value="12"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="6" Opacity="0.15" BlurRadius="25"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- أنماط النصوص -->
        <Style x:Key="CardTitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="CardValue" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>
        
        <Style x:Key="CardSubtitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.9"/>
        </Style>
        
        <!-- أنماط الأزرار المحسنة مع أنيميشن -->
        <Style x:Key="SidebarBtn" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="8"
                                Margin="8,2">
                            <Border.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"
                                              TextElement.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>

                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="#F3F4F6" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="Transparent" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                                <Setter Property="Foreground" Value="#374151"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E5E7EB"/>
                                <Setter Property="Foreground" Value="#1F2937"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- نمط الزر المفعل -->
        <Style x:Key="SidebarBtnActive" TargetType="Button">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="8"
                                Margin="8,2">
                            <Border.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="#2563EB" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="#3B82F6" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1D4ED8"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الزر المختار الجديد -->
        <Style x:Key="SidebarBtnSelected" TargetType="Button">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                CornerRadius="8"
                                Margin="8,2">
                            <Border.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"
                                              TextElement.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="#2563EB" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1.02" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="border"
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                            To="#3B82F6" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                             To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="border"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                             To="1" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1D4ED8"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernBtn" TargetType="Button">
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <!-- التخطيط الرئيسي -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- الشريط العلوي -->
            <Border Grid.Row="0" Background="White" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1" Height="70">
                <Grid Margin="24,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الترحيب على اليسار الأقصى -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                        <TextBlock Text="مرحباً بك في لوحة التحكم" FontSize="20" FontWeight="Bold" Foreground="#1F2937"
                                   FontFamily="Segoe UI, Tahoma, Arial" HorizontalAlignment="Left"/>
                    </StackPanel>

                    <!-- تنبيه الصلاة في الوسط -->
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#ECFDF5" CornerRadius="8" Padding="12,6">
                            <TextBlock Text="{Binding PrayerNotification}" FontSize="14" FontWeight="Medium" Foreground="#059669"
                                       FontFamily="Segoe UI, Tahoma, Arial" HorizontalAlignment="Center"/>
                        </Border>
                    </StackPanel>

                    <!-- الأيقونات والتاريخ على اليمين الأقصى -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                        <!-- التاريخ والوقت -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,16,0">
                            <Border Background="#F8FAFC" CornerRadius="8" Padding="12,6" Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding CurrentDateGregorian}" FontSize="14" FontWeight="Medium" Foreground="#374151"
                                               FontFamily="Segoe UI, Tahoma, Arial" Margin="0,0,6,0"/>
                                    <TextBlock Text="•" FontSize="14" Foreground="#9CA3AF" Margin="0,0,6,0"/>
                                    <TextBlock Text="{Binding CurrentDateHijri}" FontSize="14" FontWeight="Medium" Foreground="#374151"
                                               FontFamily="Segoe UI, Tahoma, Arial"/>
                                </StackPanel>
                            </Border>
                            <Border Background="#EEF2FF" CornerRadius="8" Padding="12,6">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🕐" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding CurrentTime}" FontSize="14" FontWeight="Bold" Foreground="#3730A3"
                                               FontFamily="Segoe UI, Tahoma, Arial"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- الأيقونات -->
                        <StackPanel Orientation="Horizontal">
                            <Border Background="#FEF3C7" CornerRadius="10" Padding="10" Margin="0,0,12,0">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#EF4444" CornerRadius="20" Width="8" Height="8" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                    <TextBlock Text="🔔" FontSize="18"/>
                                </StackPanel>
                            </Border>
                            <Border Background="#DBEAFE" CornerRadius="10" Padding="12">
                                <TextBlock Text="👤" FontSize="18"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>

            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="24">
                <StackPanel>
                    
                    <!-- البطاقات الإحصائية -->
                    <Grid Margin="0,0,0,32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- بطاقة الايرادات الشهرية -->
                        <Border Grid.Column="0" Background="#66BB6A" CornerRadius="12" Margin="0,0,8,0" Padding="20" Cursor="Hand" MouseLeftButtonUp="RevenueCard_Click">
                            <Border.Triggers>
                                <EventTrigger RoutedEvent="Border.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.6"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" From="30" To="0" Duration="0:0:0.6">
                                                <DoubleAnimation.EasingFunction>
                                                    <CubicEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseEnter">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="-5" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseLeave">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                            <StackPanel>
                                <Grid>
                                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                                    <TextBlock Text="الايرادات الشهرية" FontSize="12" Foreground="White" HorizontalAlignment="Right" VerticalAlignment="Top" Opacity="0.9"/>
                                </Grid>
                                <TextBlock Text="{Binding MonthlyRevenue, StringFormat='{}{0:N0}'}" FontSize="24" FontWeight="Bold" Foreground="White" HorizontalAlignment="Right" Margin="0,16,0,4"/>
                                <TextBlock Text="ريال سعودي" FontSize="11" Foreground="White" HorizontalAlignment="Right" Opacity="0.8"/>
                            </StackPanel>
                        </Border>

                        <!-- بطاقة العمال اصحاب الاشتراكات الساريه -->
                        <Border Grid.Column="1" Background="#4FC3F7" CornerRadius="12" Margin="4,0" Padding="20" Cursor="Hand" MouseLeftButtonUp="WorkersCard_Click">
                            <Border.Triggers>
                                <EventTrigger RoutedEvent="Border.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard BeginTime="0:0:0.1">
                                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.6"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" From="30" To="0" Duration="0:0:0.6">
                                                <DoubleAnimation.EasingFunction>
                                                    <CubicEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseEnter">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="-5" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseLeave">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                            <StackPanel>
                                <Grid>
                                    <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                                    <TextBlock Text="العمال اصحاب الاشتراكات الساريه" FontSize="12" Foreground="White" HorizontalAlignment="Right" VerticalAlignment="Top" Opacity="0.9"/>
                                </Grid>
                                <TextBlock Text="{Binding ActiveWorkers}" FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Right" Margin="0,16,0,4"/>
                                <TextBlock Text="عامل نشط" FontSize="11" Foreground="White" HorizontalAlignment="Right" Opacity="0.8"/>
                            </StackPanel>
                        </Border>

                        <!-- بطاقة الالتزامات المستحقة -->
                        <Border Grid.Column="2" Background="#E53E3E" CornerRadius="12" Margin="4,0" Padding="20" Cursor="Hand" MouseLeftButtonUp="ObligationsCard_Click">
                            <Border.Triggers>
                                <EventTrigger RoutedEvent="Border.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard BeginTime="0:0:0.2">
                                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.6"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" From="30" To="0" Duration="0:0:0.6">
                                                <DoubleAnimation.EasingFunction>
                                                    <CubicEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseEnter">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="-5" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseLeave">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                            <StackPanel>
                                <Grid>
                                    <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                                    <TextBlock Text="الالتزامات المستحقة" FontSize="12" Foreground="White" HorizontalAlignment="Right" VerticalAlignment="Top" Opacity="0.9"/>
                                </Grid>
                                <TextBlock Text="{Binding PendingObligations}" FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Right" Margin="0,16,0,4"/>
                                <TextBlock Text="التزام مستحق" FontSize="11" Foreground="White" HorizontalAlignment="Right" Opacity="0.8"/>
                            </StackPanel>
                        </Border>

                        <!-- بطاقة إجمالي المعاملات -->
                        <Border Grid.Column="3" Background="#9575CD" CornerRadius="12" Margin="8,0,0,0" Padding="20" Cursor="Hand" MouseLeftButtonUp="TransactionsCard_Click">
                            <Border.Triggers>
                                <EventTrigger RoutedEvent="Border.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard BeginTime="0:0:0.3">
                                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.6"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" From="30" To="0" Duration="0:0:0.6">
                                                <DoubleAnimation.EasingFunction>
                                                    <CubicEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseEnter">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1.05" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="-5" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                                <EventTrigger RoutedEvent="Border.MouseLeave">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)" To="1" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)" To="0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                            <StackPanel>
                                <Grid>
                                    <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                                    <TextBlock Text="إجمالي المعاملات" FontSize="12" Foreground="White" HorizontalAlignment="Right" VerticalAlignment="Top" Opacity="0.9"/>
                                </Grid>
                                <TextBlock Text="{Binding TotalTransactions}" FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Right" Margin="0,16,0,4"/>
                                <TextBlock Text="معاملة هذا الشهر" FontSize="11" Foreground="White" HorizontalAlignment="Right" Opacity="0.8"/>
                            </StackPanel>
                        </Border>
                    </Grid>




                    <!-- جدول الطلبات -->
                    <Border Background="White" CornerRadius="16" Margin="0,0,0,24" Padding="24">
                        <Border.Triggers>
                            <EventTrigger RoutedEvent="Border.Loaded">
                                <BeginStoryboard>
                                    <Storyboard BeginTime="0:0:0.8">
                                        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.8"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" From="40" To="0" Duration="0:0:0.8">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut"/>
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Border.Triggers>
                        <Border.RenderTransform>
                            <TranslateTransform/>
                        </Border.RenderTransform>
                        <StackPanel>
                            <Grid Margin="0,0,0,20">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <TextBlock Text="📊" FontSize="20" Foreground="#66BB6A" Margin="0,0,8,0"/>
                                    <TextBlock Text="الايرادات والمعاملات" FontSize="20" FontWeight="Bold" Foreground="#2D3748"/>
                                </StackPanel>
                                <Button Content="عرض الكل" Background="#66BB6A" Foreground="White" Style="{StaticResource ModernBtn}" HorizontalAlignment="Left"/>
                            </Grid>
                            <dxg:GridControl Name="OrdersGridControl"
                                             ItemsSource="{Binding Orders}"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             FlowDirection="RightToLeft"
                                             Height="300"
                                             MaxHeight="400">
                                <dxg:GridControl.View>
                                    <dxg:TableView ShowGroupPanel="False"
                                                   AutoWidth="True"
                                                   AllowEditing="False"
                                                   ShowIndicator="False"
                                                   ShowVerticalLines="False"
                                                   ShowHorizontalLines="False"
                                                   AlternateRowBackground="#F1F8E9"
                                                   RowMinHeight="52"
                                                   NavigationStyle="Row"
                                                   ShowFilterPanelMode="Never">
                                        <dxg:TableView.RowStyle>
                                            <Style TargetType="dxg:RowControl">
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Padding" Value="16,12"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                            </Style>
                                        </dxg:TableView.RowStyle>
                                        <dxg:TableView.ColumnHeaderStyle>
                                            <Style TargetType="dxg:GridColumnHeader">
                                                <Setter Property="Background" Value="#66BB6A"/>
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Padding" Value="16,12"/>
                                            </Style>
                                        </dxg:TableView.ColumnHeaderStyle>
                                    </dxg:TableView>
                                </dxg:GridControl.View>
                                <dxg:GridControl.Columns>
                                    <dxg:GridColumn FieldName="Date" Header="التاريخ" Width="120"/>
                                    <dxg:GridColumn FieldName="Amount" Header="المبلغ" Width="120"/>
                                    <dxg:GridColumn FieldName="Service" Header="الخدمة" Width="150"/>
                                    <dxg:GridColumn FieldName="Client" Header="العميل" Width="150"/>
                                    <dxg:GridColumn FieldName="QueryType" Header="نوع الاستعلام" Width="120"/>
                                    <dxg:GridColumn FieldName="Product" Header="المنتج" Width="100"/>
                                    <dxg:GridColumn FieldName="Status" Header="الحالة" Width="100"/>
                                </dxg:GridControl.Columns>
                            </dxg:GridControl>
                        </StackPanel>
                    </Border>
                    
                    <!-- جدول الطلبات المعلقة -->
                    <Border Background="White" CornerRadius="16" Margin="0,0,0,24" Padding="24">
                        <Border.Triggers>
                            <EventTrigger RoutedEvent="Border.Loaded">
                                <BeginStoryboard>
                                    <Storyboard BeginTime="0:0:1.0">
                                        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.8"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" From="40" To="0" Duration="0:0:0.8">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut"/>
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Border.Triggers>
                        <Border.RenderTransform>
                            <TranslateTransform/>
                        </Border.RenderTransform>
                        <StackPanel>
                            <Grid Margin="0,0,0,20">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <TextBlock Text="👥" FontSize="20" Foreground="#FFB74D" Margin="0,0,8,0"/>
                                    <TextBlock Text="العمال والمعاملات" FontSize="20" FontWeight="Bold" Foreground="#2D3748"/>
                                </StackPanel>
                                <Button Content="عرض الكل" Background="#FFB74D" Foreground="White" Style="{StaticResource ModernBtn}" HorizontalAlignment="Left"/>
                            </Grid>
                            <dxg:GridControl Name="PendingOrdersGridControl"
                                             ItemsSource="{Binding PendingOrders}"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             FlowDirection="RightToLeft"
                                             Height="300"
                                             MaxHeight="400">
                                <dxg:GridControl.View>
                                    <dxg:TableView ShowGroupPanel="False"
                                                   AutoWidth="True"
                                                   AllowEditing="False"
                                                   ShowIndicator="False"
                                                   ShowVerticalLines="False"
                                                   ShowHorizontalLines="False"
                                                   AlternateRowBackground="#FFF8E1"
                                                   RowMinHeight="52"
                                                   NavigationStyle="Row"
                                                   ShowFilterPanelMode="Never">
                                        <dxg:TableView.RowStyle>
                                            <Style TargetType="dxg:RowControl">
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Padding" Value="16,12"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                            </Style>
                                        </dxg:TableView.RowStyle>
                                        <dxg:TableView.ColumnHeaderStyle>
                                            <Style TargetType="dxg:GridColumnHeader">
                                                <Setter Property="Background" Value="#FFB74D"/>
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Padding" Value="16,12"/>
                                            </Style>
                                        </dxg:TableView.ColumnHeaderStyle>
                                    </dxg:TableView>
                                </dxg:GridControl.View>
                                <dxg:GridControl.Columns>
                                    <dxg:GridColumn FieldName="ClientName" Header="اسم العميل" Width="150"/>
                                    <dxg:GridColumn FieldName="OrderDate" Header="تاريخ الطلب" Width="120"/>
                                    <dxg:GridColumn FieldName="ServiceType" Header="نوع الخدمة" Width="150"/>
                                    <dxg:GridColumn FieldName="RequiredAmount" Header="المبلغ المطلوب" Width="140"/>
                                    <dxg:GridColumn FieldName="OrderTime" Header="وقت الطلب" Width="100"/>
                                    <dxg:GridColumn FieldName="Status" Header="الحالة" Width="120"/>
                                </dxg:GridControl.Columns>
                            </dxg:GridControl>
                        </StackPanel>
                    </Border>
                    
                </StackPanel>
            </ScrollViewer>
            
            <!-- الشريط السفلي -->
            <Border Grid.Row="2" Background="{StaticResource FooterColor}" Height="64">
                <Grid Margin="24,0">
                    <TextBlock Text="📊 التقرير الشهري للطلبات والمبيعات" Foreground="White" FontSize="16" FontWeight="SemiBold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                        <Button Content="تصدير" Background="White" Foreground="{StaticResource FooterColor}" Style="{StaticResource ModernBtn}" Margin="0,0,12,0"/>
                        <Button Content="طباعة" Background="Transparent" Foreground="White" BorderThickness="2" BorderBrush="White" Style="{StaticResource ModernBtn}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- القائمة الجانبية -->
        <Border Grid.Column="0" Background="White" BorderBrush="#E2E8F0" BorderThickness="0,0,1,0">
            <StackPanel Margin="0,20,0,0">
                <!-- شعار التطبيق -->
                <Border Background="#1E40AF" CornerRadius="8" Margin="16,0,16,32" Padding="16,12">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="📊" FontSize="20" Margin="0,0,8,0"/>
                        <TextBlock Text="لوحة التحكم" FontSize="16" FontWeight="Bold" Foreground="White"/>
                    </StackPanel>
                </Border>

                <!-- عناصر القائمة -->
                <Button Style="{StaticResource SidebarBtn}" Tag="المعاملات" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="المعاملات" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="📊" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="المعاملات عبر المنفذين" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="المعاملات عبر المنفذين" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="🔄" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="العمال" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="العمال" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="👥" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="الاقامات" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="الاقامات" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="🛂" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>



                <Button Style="{StaticResource SidebarBtn}" Tag="الحسابات المشتركه" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="الحسابات المشتركه" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="💳" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="الفواتير والسندات" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="الفواتير والسندات" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="🧾" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="الأرشيف" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="إدارة الأرشيف" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="🗄️" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>



                <Button Style="{StaticResource SidebarBtn}" Tag="المهام" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="المهام" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="📋" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="استعلام وحساب" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="استعلام وحساب" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="🧮" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="الالتزامات" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="الالتزامات" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="📋" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarBtn}" Tag="الاعدادات" Click="MenuButton_Click">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="الاعدادات" FontSize="14" FontWeight="Medium" Margin="0,0,12,0" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                        <TextBlock Text="⚙️" FontSize="16" Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
