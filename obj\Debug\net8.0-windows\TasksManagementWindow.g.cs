﻿#pragma checksum "..\..\..\TasksManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9CC4BFEF4FE630694FD4CED529F8F9908E10137E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// TasksManagementWindow
    /// </summary>
    public partial class TasksManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 50 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalTasks;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNewTasks;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInProgressTasks;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCompletedTasks;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtGridInfo;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridTasks;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnAddTask;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnEditTask;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnDeleteTask;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\TasksManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnExport;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/tasksmanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\TasksManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTotalTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtNewTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtInProgressTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtCompletedTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtGridInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.GridTasks = ((DevExpress.Xpf.Grid.GridControl)(target));
            
            #line 149 "..\..\..\TasksManagementWindow.xaml"
            this.GridTasks.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.GridTasks_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnAddTask = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 204 "..\..\..\TasksManagementWindow.xaml"
            this.BtnAddTask.Click += new System.Windows.RoutedEventHandler(this.BtnAddTask_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnEditTask = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 218 "..\..\..\TasksManagementWindow.xaml"
            this.BtnEditTask.Click += new System.Windows.RoutedEventHandler(this.BtnEditTask_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnDeleteTask = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 232 "..\..\..\TasksManagementWindow.xaml"
            this.BtnDeleteTask.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteTask_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnExport = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 246 "..\..\..\TasksManagementWindow.xaml"
            this.BtnExport.Click += new System.Windows.RoutedEventHandler(this.BtnExport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

