﻿#pragma checksum "..\..\..\AddTransactionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1E6EE9277B06092840D395678DAB90FC97E6AD0A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Xpf.DXBinding;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// AddTransactionWindow
    /// </summary>
    public partial class AddTransactionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 74 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ServiceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServiceFeesTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GovernmentFeesTextBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LaborOfficeFeesTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NetProfitTextBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TotalAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsEmployedCheckBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmploymentPanel;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmploymentAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\AddTransactionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/addtransactionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AddTransactionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ClientNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ServiceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ServiceFeesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 112 "..\..\..\AddTransactionWindow.xaml"
            this.ServiceFeesTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NumericTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.GovernmentFeesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 118 "..\..\..\AddTransactionWindow.xaml"
            this.GovernmentFeesTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NumericTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LaborOfficeFeesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 124 "..\..\..\AddTransactionWindow.xaml"
            this.LaborOfficeFeesTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NumericTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.NetProfitTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 130 "..\..\..\AddTransactionWindow.xaml"
            this.NetProfitTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NumericTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TotalAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.IsEmployedCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 165 "..\..\..\AddTransactionWindow.xaml"
            this.IsEmployedCheckBox.Checked += new System.Windows.RoutedEventHandler(this.IsEmployedCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 166 "..\..\..\AddTransactionWindow.xaml"
            this.IsEmployedCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.IsEmployedCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.EmploymentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.EmploymentAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 172 "..\..\..\AddTransactionWindow.xaml"
            this.EmploymentAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NumericTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\AddTransactionWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 189 "..\..\..\AddTransactionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

