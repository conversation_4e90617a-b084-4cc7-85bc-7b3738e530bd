﻿#pragma checksum "..\..\..\CommitmentsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "10759C0F6BD2A346918EB22E737C5D787A63AD01"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Bars;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using DevExpress.Xpf.LayoutControl;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// CommitmentsWindow
    /// </summary>
    public partial class CommitmentsWindow : DevExpress.Xpf.Core.ThemedWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton AddCommitmentBtn;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit StatusFilterCombo;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CategoryFilterCombo;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton ClearFiltersBtn;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl CommitmentsGrid;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.TableView CommitmentsTableView;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Bars.BarButtonItem EditMenuItem;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Bars.BarButtonItem PayMenuItem;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\CommitmentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Bars.BarButtonItem DeleteMenuItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/commitmentswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\CommitmentsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddCommitmentBtn = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 43 "..\..\..\CommitmentsWindow.xaml"
            this.AddCommitmentBtn.Click += new System.Windows.RoutedEventHandler(this.AddCommitmentBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshBtn = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 53 "..\..\..\CommitmentsWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((DevExpress.Xpf.Editors.TextEdit)(target));
            
            #line 79 "..\..\..\CommitmentsWindow.xaml"
            this.SearchTextBox.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.StatusFilterCombo = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 87 "..\..\..\CommitmentsWindow.xaml"
            this.StatusFilterCombo.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.StatusFilterCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CategoryFilterCombo = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 101 "..\..\..\CommitmentsWindow.xaml"
            this.CategoryFilterCombo.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.CategoryFilterCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ClearFiltersBtn = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 110 "..\..\..\CommitmentsWindow.xaml"
            this.ClearFiltersBtn.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CommitmentsGrid = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 8:
            this.CommitmentsTableView = ((DevExpress.Xpf.Grid.TableView)(target));
            return;
            case 9:
            this.EditMenuItem = ((DevExpress.Xpf.Bars.BarButtonItem)(target));
            
            #line 140 "..\..\..\CommitmentsWindow.xaml"
            this.EditMenuItem.ItemClick += new DevExpress.Xpf.Bars.ItemClickEventHandler(this.EditMenuItem_ItemClick);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PayMenuItem = ((DevExpress.Xpf.Bars.BarButtonItem)(target));
            
            #line 141 "..\..\..\CommitmentsWindow.xaml"
            this.PayMenuItem.ItemClick += new DevExpress.Xpf.Bars.ItemClickEventHandler(this.PayMenuItem_ItemClick);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DeleteMenuItem = ((DevExpress.Xpf.Bars.BarButtonItem)(target));
            
            #line 142 "..\..\..\CommitmentsWindow.xaml"
            this.DeleteMenuItem.ItemClick += new DevExpress.Xpf.Bars.ItemClickEventHandler(this.DeleteMenuItem_ItemClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

