﻿#pragma checksum "..\..\..\SharedAccountsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "94E0F55004EE257BFAC0BF03DA2FD46DA2E6E006"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Bars;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// SharedAccountsWindow
    /// </summary>
    public partial class SharedAccountsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 119 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAccounts;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtMonthlyAmount;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalProfit;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddAccount;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEditAccount;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteAccount;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshData;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridAccounts;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InputPanel;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormTitle;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtAccountName;
        
        #line default
        #line hidden
        
        
        #line 523 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtWorkerName;
        
        #line default
        #line hidden
        
        
        #line 529 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateTransaction;
        
        #line default
        #line hidden
        
        
        #line 534 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbServiceType;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinTotalGrossAmount;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinTotalExpenses;
        
        #line default
        #line hidden
        
        
        #line 590 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinSponsorAmount;
        
        #line default
        #line hidden
        
        
        #line 595 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelOfficeAmount;
        
        #line default
        #line hidden
        
        
        #line 597 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinOfficeAmount;
        
        #line default
        #line hidden
        
        
        #line 604 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinPassportFees;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 611 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblWorkPermitFeesTitle;
        
        #line default
        #line hidden
        
        
        #line 612 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 619 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinRecruitmentAmount;
        
        #line default
        #line hidden
        
        
        #line 626 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinServiceFees;
        
        #line default
        #line hidden
        
        
        #line 643 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 645 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 650 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelTransferFees;
        
        #line default
        #line hidden
        
        
        #line 652 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinTransferFees;
        
        #line default
        #line hidden
        
        
        #line 659 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinNetProfit;
        
        #line default
        #line hidden
        
        
        #line 686 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblTotalGrossAmount;
        
        #line default
        #line hidden
        
        
        #line 693 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblTotalExpenses;
        
        #line default
        #line hidden
        
        
        #line 700 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblNetProfit;
        
        #line default
        #line hidden
        
        
        #line 707 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblProfitShare;
        
        #line default
        #line hidden
        
        
        #line 735 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblSponsorAmount;
        
        #line default
        #line hidden
        
        
        #line 738 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelLblOfficeAmount;
        
        #line default
        #line hidden
        
        
        #line 741 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblOfficeAmount;
        
        #line default
        #line hidden
        
        
        #line 747 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblPassportFees;
        
        #line default
        #line hidden
        
        
        #line 750 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelLblWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 751 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblWorkPermitFeesIcon;
        
        #line default
        #line hidden
        
        
        #line 752 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblWorkPermitFeesText;
        
        #line default
        #line hidden
        
        
        #line 753 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 759 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblRecruitmentAmount;
        
        #line default
        #line hidden
        
        
        #line 765 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblServiceFees;
        
        #line default
        #line hidden
        
        
        #line 769 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelLblMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 772 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 776 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PanelLblTransferFees;
        
        #line default
        #line hidden
        
        
        #line 779 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblTransferFees;
        
        #line default
        #line hidden
        
        
        #line 791 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 800 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.MemoEdit TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 805 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 811 "..\..\..\SharedAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/sharedaccountswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SharedAccountsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTotalAccounts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtMonthlyAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtTotalProfit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BtnAddAccount = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnAddAccount.Click += new System.Windows.RoutedEventHandler(this.BtnAddAccount_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnEditAccount = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnEditAccount.Click += new System.Windows.RoutedEventHandler(this.BtnEditAccount_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnDeleteAccount = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnDeleteAccount.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteAccount_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnRefreshData = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnRefreshData.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.GridAccounts = ((DevExpress.Xpf.Grid.GridControl)(target));
            
            #line 224 "..\..\..\SharedAccountsWindow.xaml"
            this.GridAccounts.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.GridAccounts_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InputPanel = ((System.Windows.Controls.Border)(target));
            
            #line 480 "..\..\..\SharedAccountsWindow.xaml"
            this.InputPanel.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.InputPanel_MouseDown);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FormTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtAccountName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 13:
            this.TxtWorkerName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 14:
            this.DateTransaction = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 15:
            this.CmbServiceType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 535 "..\..\..\SharedAccountsWindow.xaml"
            this.CmbServiceType.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.CmbServiceType_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SpinTotalGrossAmount = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 559 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinTotalGrossAmount.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SpinTotalExpenses = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 18:
            this.SpinSponsorAmount = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 592 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinSponsorAmount.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.PanelOfficeAmount = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 20:
            this.SpinOfficeAmount = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 599 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinOfficeAmount.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SpinPassportFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 606 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinPassportFees.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 22:
            this.PanelWorkPermitFees = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.LblWorkPermitFeesTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.SpinWorkPermitFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 614 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinWorkPermitFees.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SpinRecruitmentAmount = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 621 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinRecruitmentAmount.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 26:
            this.SpinServiceFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 27:
            this.PanelMedicalInsurance = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            this.SpinMedicalInsurance = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 647 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinMedicalInsurance.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 29:
            this.PanelTransferFees = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 30:
            this.SpinTransferFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            
            #line 654 "..\..\..\SharedAccountsWindow.xaml"
            this.SpinTransferFees.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.SpinAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 31:
            this.SpinNetProfit = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 32:
            this.LblTotalGrossAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.LblTotalExpenses = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.LblNetProfit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.LblProfitShare = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.LblSponsorAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.PanelLblOfficeAmount = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 38:
            this.LblOfficeAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.LblPassportFees = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.PanelLblWorkPermitFees = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 41:
            this.LblWorkPermitFeesIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.LblWorkPermitFeesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.LblWorkPermitFees = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.LblRecruitmentAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.LblServiceFees = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.PanelLblMedicalInsurance = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 47:
            this.LblMedicalInsurance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.PanelLblTransferFees = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 49:
            this.LblTransferFees = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.LblTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.TxtNotes = ((DevExpress.Xpf.Editors.MemoEdit)(target));
            return;
            case 52:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 808 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 814 "..\..\..\SharedAccountsWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

