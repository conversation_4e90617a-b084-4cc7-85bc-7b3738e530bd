﻿#pragma checksum "..\..\..\AgentTransactionsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "51D992D38F5B7B46984884989D58E5FB60A9A5BA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// AgentTransactionsWindow
    /// </summary>
    public partial class AgentTransactionsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddTransaction;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewTransactions;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContent;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer InputSection;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAgentName;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServiceType;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServiceFees;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNetProfit;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbTransferStatus;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatus;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DataSection;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEdit;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnChangeStatus;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDelete;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridAgentTransactions;
        
        #line default
        #line hidden
        
        
        #line 505 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAgentTransactions;
        
        #line default
        #line hidden
        
        
        #line 516 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPendingAgentTransactions;
        
        #line default
        #line hidden
        
        
        #line 527 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCompletedAgentTransactions;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\AgentTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAgentProfits;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/agenttransactionswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AgentTransactionsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnAddTransaction = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnAddTransaction.Click += new System.Windows.RoutedEventHandler(this.BtnAddTransaction_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnViewTransactions = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnViewTransactions.Click += new System.Windows.RoutedEventHandler(this.BtnViewTransactions_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MainContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.InputSection = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 5:
            this.TxtAgentName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtServiceType = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.TxtServiceFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 96 "..\..\..\AgentTransactionsWindow.xaml"
            this.TxtServiceFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateNetProfit);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 102 "..\..\..\AgentTransactionsWindow.xaml"
            this.TxtTotalAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateNetProfit);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TxtNetProfit = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CmbTransferStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.CmbStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.DataSection = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.BtnEdit = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnEdit.Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.BtnChangeStatus = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnChangeStatus.Click += new System.Windows.RoutedEventHandler(this.BtnChangeStatus_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnDelete = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\AgentTransactionsWindow.xaml"
            this.BtnDelete.Click += new System.Windows.RoutedEventHandler(this.BtnDelete_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 203 "..\..\..\AgentTransactionsWindow.xaml"
            this.TxtSearch.GotFocus += new System.Windows.RoutedEventHandler(this.TxtSearch_GotFocus);
            
            #line default
            #line hidden
            
            #line 203 "..\..\..\AgentTransactionsWindow.xaml"
            this.TxtSearch.LostFocus += new System.Windows.RoutedEventHandler(this.TxtSearch_LostFocus);
            
            #line default
            #line hidden
            
            #line 204 "..\..\..\AgentTransactionsWindow.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.GridAgentTransactions = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 21:
            this.TxtTotalAgentTransactions = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TxtPendingAgentTransactions = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.TxtCompletedAgentTransactions = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.TxtTotalAgentProfits = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

