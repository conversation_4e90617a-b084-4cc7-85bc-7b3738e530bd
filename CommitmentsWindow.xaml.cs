using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using System.Diagnostics;

namespace ArabicDashboard
{
    public partial class CommitmentsWindow : ThemedWindow
    {
        private readonly DatabaseService _databaseService;
        private List<Commitment> _commitments;
        private List<Commitment> _filteredCommitments;

        public CommitmentsWindow()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _commitments = new List<Commitment>();
            _filteredCommitments = new List<Commitment>();

            Loaded += CommitmentsWindow_Loaded;
        }

        private async void CommitmentsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCommitments();
        }

        private async Task LoadCommitments()
        {
            try
            {
                _commitments = await _databaseService.GetCommitmentsAsync();
                _filteredCommitments = new List<Commitment>(_commitments);
                CommitmentsGrid.ItemsSource = _filteredCommitments;
                
                Debug.WriteLine($"تم تحميل {_commitments.Count} التزام");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحميل الالتزامات: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل الالتزامات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddCommitmentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new AddEditCommitmentWindow();
                if (addWindow.ShowDialog() == true)
                {
                    await LoadCommitments();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في فتح نافذة إضافة الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            await LoadCommitments();
        }

        private void SearchTextBox_TextChanged(object sender, DevExpress.Xpf.Editors.EditValueChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilterCombo_SelectionChanged(object sender, DevExpress.Xpf.Editors.EditValueChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryFilterCombo_SelectionChanged(object sender, DevExpress.Xpf.Editors.EditValueChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearFiltersBtn_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            StatusFilterCombo.SelectedIndex = 0;
            CategoryFilterCombo.SelectedIndex = 0;
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _commitments.AsEnumerable();

                // فلتر البحث النصي
                if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(c => 
                        c.CommitmentName.ToLower().Contains(searchText) ||
                        c.CommitmentType.ToLower().Contains(searchText) ||
                        c.Description.ToLower().Contains(searchText));
                }

                // فلتر الحالة
                if (StatusFilterCombo.SelectedItem != null && StatusFilterCombo.Text != "الكل")
                {
                    var status = StatusFilterCombo.Text;
                    if (status == "قريب الاستحقاق")
                    {
                        filtered = filtered.Where(c => c.IsNearDue);
                    }
                    else if (status == "متأخر")
                    {
                        filtered = filtered.Where(c => c.IsOverdue);
                    }
                    else
                    {
                        filtered = filtered.Where(c => c.Status == status);
                    }
                }

                // فلتر التصنيف
                if (CategoryFilterCombo.SelectedItem != null && CategoryFilterCombo.Text != "الكل")
                {
                    filtered = filtered.Where(c => c.CommitmentCategory == CategoryFilterCombo.Text);
                }

                _filteredCommitments = filtered.ToList();
                CommitmentsGrid.ItemsSource = _filteredCommitments;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        private async void EditMenuItem_ItemClick(object sender, DevExpress.Xpf.Bars.ItemClickEventArgs e)
        {
            try
            {
                if (CommitmentsTableView.FocusedRowData.Row is Commitment selectedCommitment)
                {
                    var editWindow = new AddEditCommitmentWindow(selectedCommitment);
                    if (editWindow.ShowDialog() == true)
                    {
                        await LoadCommitments();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تعديل الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تعديل الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PayMenuItem_ItemClick(object sender, DevExpress.Xpf.Bars.ItemClickEventArgs e)
        {
            try
            {
                if (CommitmentsTableView.FocusedRowData.Row is Commitment selectedCommitment)
                {
                    if (selectedCommitment.Status == "مسدد")
                    {
                        System.Windows.MessageBox.Show("هذا الالتزام مسدد بالفعل", "تنبيه",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    var result = System.Windows.MessageBox.Show($"هل تريد تسديد الالتزام '{selectedCommitment.CommitmentName}'؟",
                        "تأكيد التسديد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        selectedCommitment.Status = "مسدد";
                        selectedCommitment.PaymentDate = DateTime.Now;
                        selectedCommitment.UpdatedDate = DateTime.Now;

                        await _databaseService.UpdateCommitmentAsync(selectedCommitment);
                        await LoadCommitments();

                        System.Windows.MessageBox.Show("تم تسديد الالتزام بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تسديد الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تسديد الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteMenuItem_ItemClick(object sender, DevExpress.Xpf.Bars.ItemClickEventArgs e)
        {
            try
            {
                if (CommitmentsTableView.FocusedRowData.Row is Commitment selectedCommitment)
                {
                    var result = System.Windows.MessageBox.Show($"هل تريد حذف الالتزام '{selectedCommitment.CommitmentName}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        await _databaseService.DeleteCommitmentAsync(selectedCommitment.Id);
                        await LoadCommitments();

                        System.Windows.MessageBox.Show("تم حذف الالتزام بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في حذف الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في حذف الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
