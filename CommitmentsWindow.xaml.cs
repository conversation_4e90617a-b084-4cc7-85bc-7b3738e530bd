using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using DevExpress.Xpf.Core;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using System.Diagnostics;

namespace ArabicDashboard
{
    public partial class CommitmentsWindow : ThemedWindow
    {
        private readonly DatabaseService _databaseService;
        private List<Commitment> _commitments;

        public CommitmentsWindow()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _commitments = new List<Commitment>();

            Loaded += CommitmentsWindow_Loaded;
        }

        private async Task LoadCommitments()
        {
            try
            {
                _commitments = await _databaseService.GetCommitmentsAsync();
                CommitmentsGrid.ItemsSource = _commitments;

                Debug.WriteLine($"تم تحميل {_commitments.Count} التزام");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحميل الالتزامات: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل الالتزامات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public async Task<List<Commitment>> GetCommitmentsAsync()
        {
            try
            {
                var commitments = await _databaseService.GetCommitmentsAsync();
                return commitments;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetCommitmentsAsync: {ex.Message}");
                return new List<Commitment>();
            }
        }

        private async void AddCommitmentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new AddEditCommitmentWindow();
                if (addWindow.ShowDialog() == true)
                {
                    await LoadCommitments();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في فتح نافذة إضافة الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الالتزام: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            await LoadCommitments();
        }
    }
}
