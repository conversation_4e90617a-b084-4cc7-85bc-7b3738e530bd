﻿using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Threading;
using System.Globalization;
using System.ComponentModel;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Controls;
using System.Net.Http;
using System.Text.RegularExpressions;
using ArabicDashboard.Services;

namespace ArabicDashboard;

public class SelectedPageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string selectedPage)
        {
            if (parameter is string pageName)
            {
                return selectedPage == pageName;
            }
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window, INotifyPropertyChanged
{
    public ObservableCollection<OrderData> Orders { get; set; } = new();
    public ObservableCollection<PendingOrderData> PendingOrders { get; set; } = new();

    private string _currentTime = "";
    public string CurrentTime
    {
        get => _currentTime;
        set
        {
            _currentTime = value;
            OnPropertyChanged(nameof(CurrentTime));
        }
    }

    private string _currentDateGregorian = "";
    public string CurrentDateGregorian
    {
        get => _currentDateGregorian;
        set
        {
            _currentDateGregorian = value;
            OnPropertyChanged(nameof(CurrentDateGregorian));
        }
    }

    private string _currentDateHijri = "";
    public string CurrentDateHijri
    {
        get => _currentDateHijri;
        set
        {
            _currentDateHijri = value;
            OnPropertyChanged(nameof(CurrentDateHijri));
        }
    }

    private string _prayerNotification = "";
    public string PrayerNotification
    {
        get => _prayerNotification;
        set
        {
            _prayerNotification = value;
            OnPropertyChanged(nameof(PrayerNotification));
        }
    }

    private DispatcherTimer? _timer;
    private DispatcherTimer? _prayerUpdateTimer;
    private string _selectedPage = "المعاملات";
    private static readonly HttpClient httpClient = new HttpClient();
    private Dictionary<string, TimeSpan>? _cachedPrayerTimes;

    // خدمة قاعدة البيانات
    private readonly DatabaseService _databaseService;

    // خصائص الإحصائيات التفاعلية
    private decimal _monthlyRevenue = 0;
    public decimal MonthlyRevenue
    {
        get => _monthlyRevenue;
        set
        {
            _monthlyRevenue = value;
            OnPropertyChanged(nameof(MonthlyRevenue));
        }
    }

    private int _activeWorkers = 0;
    public int ActiveWorkers
    {
        get => _activeWorkers;
        set
        {
            _activeWorkers = value;
            OnPropertyChanged(nameof(ActiveWorkers));
        }
    }

    private int _pendingObligations = 0;
    public int PendingObligations
    {
        get => _pendingObligations;
        set
        {
            _pendingObligations = value;
            OnPropertyChanged(nameof(PendingObligations));
        }
    }

    private int _totalObligations = 0;
    public int TotalObligations
    {
        get => _totalObligations;
        set
        {
            _totalObligations = value;
            OnPropertyChanged(nameof(TotalObligations));
        }
    }

    private int _totalTransactions = 0;
    public int TotalTransactions
    {
        get => _totalTransactions;
        set
        {
            _totalTransactions = value;
            OnPropertyChanged(nameof(TotalTransactions));
        }
    }
    public string SelectedPage
    {
        get => _selectedPage;
        set
        {
            _selectedPage = value;
            OnPropertyChanged(nameof(SelectedPage));
            UpdateButtonStyles();
        }
    }

    public MainWindow()
    {
        InitializeComponent();

        // تهيئة خدمة قاعدة البيانات
        _databaseService = new DatabaseService();

        InitializeData();
        LoadDataIntoGrids();
        InitializeTimer();
        InitializeStatistics();

        // تحميل الإحصائيات من قاعدة البيانات
        _ = Task.Run(LoadStatisticsFromDatabase);
    }

    private void InitializeStatistics()
    {
        // تصفير جميع الإحصائيات
        MonthlyRevenue = 0;
        ActiveWorkers = 0;
        PendingObligations = 0;
        TotalObligations = 0;
        TotalTransactions = 0;

        // ربط البيانات بالواجهة
        DataContext = this;

        // تحديث حالة الأزرار الافتراضية
        this.Loaded += (s, e) => UpdateButtonStates();
    }

    public async void UpdateStatistics()
    {
        try
        {
            // تحديث الإيرادات الشهرية من المعاملات وصافي الربح تلقائياً
            await UpdateMonthlyRevenue();

            // تحديث عدد العمال أصحاب الاشتراكات السارية
            UpdateActiveWorkers();

            // تحديث إجمالي المعاملات
            await UpdateTotalTransactions();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    public void UpdateDashboardStatistics()
    {
        try
        {
            // تحديث بطاقات الإحصائيات في الواجهة
            OnPropertyChanged(nameof(MonthlyRevenue));
            OnPropertyChanged(nameof(ActiveWorkers));
            OnPropertyChanged(nameof(PendingObligations));
            OnPropertyChanged(nameof(TotalTransactions));
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث بطاقات الإحصائيات: {ex.Message}");
        }
    }

    private async Task UpdateMonthlyRevenue()
    {
        try
        {
            // جلب صافي الربح الشهري من قاعدة البيانات تلقائياً
            var revenue = await _databaseService.GetTotalMonthlyRevenueAsync();
            MonthlyRevenue = revenue;
            System.Diagnostics.Debug.WriteLine($"تم تحديث الإيرادات الشهرية تلقائياً: {revenue}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإيرادات الشهرية: {ex.Message}");
            MonthlyRevenue = 0;
        }
    }

    private void UpdateActiveWorkers()
    {
        // هذا سيتم ربطه مع صفحة العمال
        // ActiveWorkers = GetActiveWorkersCount();
        ActiveWorkers = 0; // مؤقتاً حتى يتم ربطه
    }



    private async Task UpdateTotalTransactions()
    {
        try
        {
            // جلب العدد الإجمالي للمعاملات من قاعدة البيانات
            var total = await _databaseService.GetTotalTransactionsCountAsync();
            TotalTransactions = total;
            System.Diagnostics.Debug.WriteLine($"تم تحديث إجمالي المعاملات: {total}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث إجمالي المعاملات: {ex.Message}");
            TotalTransactions = 0;
        }
    }

    // تحميل الإحصائيات من قاعدة البيانات
    private async Task LoadStatisticsFromDatabase()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("بدء تحميل الإحصائيات من قاعدة البيانات...");

            // تحميل الإيرادات الشهرية
            var monthlyRevenue = await _databaseService.GetTotalMonthlyRevenueAsync();

            // تحميل عدد المعاملات الإجمالي
            var totalTransactions = await _databaseService.GetTotalTransactionsCountAsync();

            // تحميل عدد العمال النشطين
            var activeWorkers = await _databaseService.GetActiveWorkersCountAsync();



            // تحديث الواجهة في الخيط الرئيسي
            Dispatcher.Invoke(() =>
            {
                MonthlyRevenue = monthlyRevenue;
                TotalTransactions = totalTransactions;
                ActiveWorkers = activeWorkers;
                PendingObligations = 0; // تم حذف صفحة الالتزامات
                TotalObligations = 15; // قيمة افتراضية للالتزامات الشخصية

                System.Diagnostics.Debug.WriteLine($"تم تحميل الإحصائيات:");
                System.Diagnostics.Debug.WriteLine($"- الإيرادات الشهرية: {monthlyRevenue}");
                System.Diagnostics.Debug.WriteLine($"- إجمالي المعاملات: {totalTransactions}");
                System.Diagnostics.Debug.WriteLine($"- العمال النشطين: {activeWorkers}");
                System.Diagnostics.Debug.WriteLine($"- الالتزامات الشخصية: {TotalObligations}");

            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات من قاعدة البيانات: {ex.Message}");
        }
    }

    // تحديث الإحصائيات من قاعدة البيانات (يتم استدعاؤها من النوافذ الأخرى)
    public async Task RefreshStatisticsFromDatabase()
    {
        await LoadStatisticsFromDatabase();
    }

    // تحديث الإحصائيات عند التركيز على النافذة الرئيسية
    protected override void OnActivated(EventArgs e)
    {
        base.OnActivated(e);

        // تحديث الإحصائيات عند العودة للنافذة الرئيسية
        _ = Task.Run(LoadStatisticsFromDatabase);
    }

    private void UpdateButtonStyles()
    {
        // البحث عن جميع أزرار القائمة الجانبية وتحديث أنماطها
        var sidebarButtons = FindVisualChildren<System.Windows.Controls.Button>(this).Where(b => b.Tag is string);

        foreach (var button in sidebarButtons)
        {
            if (button.Tag is string pageName)
            {
                if (pageName == SelectedPage)
                {
                    button.Style = (Style)FindResource("SidebarBtnSelected");
                    // التأكد من أن الكتابة بيضاء
                    SetTextBlockColors(button, System.Windows.Media.Brushes.White);
                }
                else
                {
                    button.Style = (Style)FindResource("SidebarBtn");
                    // التأكد من أن الكتابة رمادية
                    SetTextBlockColors(button, new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x6B, 0x72, 0x80)));
                }
            }
        }
    }

    private void SetTextBlockColors(System.Windows.Controls.Button button, System.Windows.Media.Brush color)
    {
        var textBlocks = FindVisualChildren<TextBlock>(button);
        foreach (var textBlock in textBlocks)
        {
            textBlock.Foreground = color;
        }
    }

    private async void MenuButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is System.Windows.Controls.Button button)
        {
            if (button.Tag is string pageName)
            {
            // تحديث الصفحة المحددة
            SelectedPage = pageName;

            // تحديث مظهر الأزرار
            UpdateButtonStates();

            // إضافة تأثير بصري للزر المضغوط
            button.Opacity = 0.7;
            await Task.Delay(100);
            button.Opacity = 1.0;

            // فتح النوافذ المخصصة للصفحات
            if (pageName == "المعاملات")
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("محاولة فتح نافذة المعاملات من MainWindow...");
                    var transactionsWindow = new SimpleTransactionsWindow();
                    transactionsWindow.Show();
                    System.Diagnostics.Debug.WriteLine("تم إنشاء وعرض SimpleTransactionsWindow بنجاح");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح نافذة المعاملات من MainWindow: {ex.Message}");
                    System.Windows.MessageBox.Show($"خطأ في فتح نافذة المعاملات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "المعاملات عبر المنفذين")
            {
                try
                {
                    var agentTransactionsWindow = new AgentTransactionsWindow();
                    agentTransactionsWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في فتح نافذة المعاملات عبر المنفذين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "العمال")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة العمال
                    var workersWindow = new WorkersWindow();
                    workersWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة العمال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "الاقامات")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة الإقامات
                    var residencesWindow = new ResidencesWindow();
                    residencesWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الإقامات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }

            else if (pageName == "الحسابات المشتركه")
            {
                try
                {
                    Console.WriteLine("🔍 MainWindow: محاولة فتح صفحة الحسابات المشتركة...");

                    // تحديث الإحصائيات أولاً
                    Console.WriteLine("🔄 MainWindow: تحديث الإحصائيات...");
                    await RefreshStatisticsFromDatabase();
                    Console.WriteLine("✅ MainWindow: تم تحديث الإحصائيات");

                    // فتح صفحة الحسابات المشتركة
                    Console.WriteLine("🔄 MainWindow: إنشاء نافذة الحسابات المشتركة...");
                    var sharedAccountsWindow = new SharedAccountsWindow();
                    Console.WriteLine("✅ MainWindow: تم إنشاء النافذة");

                    Console.WriteLine("🔄 MainWindow: عرض النافذة...");
                    sharedAccountsWindow.Show();
                    Console.WriteLine("✅ MainWindow: تم عرض النافذة بنجاح");

                    // إضافة تأخير قصير للتأكد من التحميل
                    await Task.Delay(1000);
                    Console.WriteLine("🔍 MainWindow: انتهاء عملية فتح الصفحة");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في فتح صفحة الحسابات المشتركة: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"📋 Stack Trace: {ex.StackTrace}");

                    if (ex.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 الخطأ الداخلي: {ex.InnerException.Message}");
                        System.Diagnostics.Debug.WriteLine($"📋 Stack Trace الداخلي: {ex.InnerException.StackTrace}");
                    }

                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الحسابات المشتركة:\n\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}\n\nيرجى التحقق من سجل الأخطاء للمزيد من التفاصيل.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "الفواتير والسندات")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة الفواتير والسندات
                    var invoicesWindow = new InvoicesWindow();
                    invoicesWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح صفحة الفواتير والسندات: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الفواتير والسندات:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "الأرشيف")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة إدارة الأرشيف
                    var archiveWindow = new ArchiveWindow();
                    archiveWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح صفحة الأرشيف: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الأرشيف:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "المهام")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة المهام
                    var tasksWindow = new TasksManagementWindow();
                    tasksWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح صفحة المهام: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة المهام:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "استعلام وحساب")
            {
                try
                {
                    // فتح صفحة الاستعلام والحساب
                    var inquiryWindow = new InquiryCalculationWindow();
                    inquiryWindow.Show();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح صفحة الاستعلام والحساب: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الاستعلام والحساب:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else if (pageName == "الالتزامات")
            {
                try
                {
                    // تحديث الإحصائيات أولاً
                    await RefreshStatisticsFromDatabase();

                    // فتح صفحة الالتزامات - سيتم تنفيذها لاحقاً
                    System.Windows.MessageBox.Show("صفحة الالتزامات قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح صفحة الالتزامات: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                    System.Windows.MessageBox.Show($"خطأ في فتح صفحة الالتزامات:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            }

        }
    }

    // تحديث حالة الأزرار
    private void UpdateButtonStates()
    {
        try
        {
            // البحث عن جميع أزرار القائمة الجانبية
            var sidebarButtons = FindVisualChildren<System.Windows.Controls.Button>(this)
                .Where(b => b.Style?.ToString().Contains("SidebarBtn") == true);

            foreach (var btn in sidebarButtons)
            {
                if (btn.Tag?.ToString() == SelectedPage)
                {
                    // الزر النشط
                    btn.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(59, 130, 246)); // أزرق
                    btn.Foreground = System.Windows.Media.Brushes.White;
                }
                else
                {
                    // الأزرار غير النشطة
                    btn.Background = System.Windows.Media.Brushes.Transparent;
                    btn.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(156, 163, 175)); // رمادي
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الأزرار: {ex.Message}");
        }
    }

    // مساعد للبحث عن العناصر الفرعية
    private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
    {
        if (depObj != null)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                if (child != null)
                {
                    if (child is T)
                    {
                        yield return (T)child;
                    }
                }

                if (child != null)
                {
                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
    }

    private void InitializeTimer()
    {
        // تايمر تحديث الوقت والتاريخ كل ثانية
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1);
        _timer.Tick += Timer_Tick;
        _timer.Start();

        // تايمر تحديث أوقات الصلاة كل ساعة
        _prayerUpdateTimer = new DispatcherTimer();
        _prayerUpdateTimer.Interval = TimeSpan.FromHours(1);
        _prayerUpdateTimer.Tick += PrayerUpdateTimer_Tick;
        _prayerUpdateTimer.Start();

        // تحديث فوري للبيانات
        UpdateDateTime();
        _ = Task.Run(UpdatePrayerTimesCache);
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        UpdateDateTime();
    }

    private void PrayerUpdateTimer_Tick(object? sender, EventArgs e)
    {
        _ = Task.Run(UpdatePrayerTimesCache);
    }

    private async Task UpdatePrayerTimesCache()
    {
        var newTimes = await FetchPrayerTimesFromWebsite();
        if (newTimes != null)
        {
            _cachedPrayerTimes = newTimes;
            // إجبار تحديث تنبيه الصلاة فور<|im_start|>
            Dispatcher.Invoke(() => UpdatePrayerNotification(DateTime.Now));
        }
    }

    private void UpdateDateTime()
    {
        var now = DateTime.Now;

        // الوقت بنظام 12 ساعة مع "ص" و "م"
        var timeString = now.ToString("hh:mm:ss tt", CultureInfo.CreateSpecificCulture("en-US"));
        CurrentTime = timeString.Replace("AM", "ص").Replace("PM", "م");

        // التاريخ الميلادي
        CurrentDateGregorian = now.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture);

        // التاريخ الهجري
        var hijriCalendar = new UmAlQuraCalendar();
        var hijriYear = hijriCalendar.GetYear(now);
        var hijriMonth = hijriCalendar.GetMonth(now);
        var hijriDay = hijriCalendar.GetDayOfMonth(now);

        string[] hijriMonths = { "", "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأول",
                                "جمادى الثاني", "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة" };

        CurrentDateHijri = $"{hijriDay} {hijriMonths[hijriMonth]} {hijriYear}هـ";

        // تنبيه الصلاة لمحافظة بيش
        UpdatePrayerNotification(now);
    }

    private void UpdatePrayerNotification(DateTime now)
    {
        // أوقات الصلاة الدقيقة لمحافظة بيش (يتم تحديثها حسب الموسم)
        var prayerTimes = GetPrayerTimesForBish(now);

        // إضافة أوقات الصلاة الأساسية فقط (بدون الشروق)
        var mainPrayers = new Dictionary<string, TimeSpan>
        {
            { "الفجر", prayerTimes["الفجر"] },
            { "الظهر", prayerTimes["الظهر"] },
            { "العصر", prayerTimes["العصر"] },
            { "المغرب", prayerTimes["المغرب"] },
            { "العشاء", prayerTimes["العشاء"] }
        };

        var currentTime = now.TimeOfDay;
        string nextPrayer = "";
        TimeSpan nextPrayerTime = TimeSpan.Zero;
        TimeSpan minDifference = TimeSpan.MaxValue;

        foreach (var prayer in mainPrayers)
        {
            var timeDifference = prayer.Value - currentTime;
            if (timeDifference < TimeSpan.Zero)
                timeDifference = timeDifference.Add(TimeSpan.FromDays(1));

            if (timeDifference < minDifference)
            {
                minDifference = timeDifference;
                nextPrayer = prayer.Key;
                nextPrayerTime = prayer.Value;
            }
        }

        if (minDifference.TotalMinutes <= 15)
        {
            PrayerNotification = $"🕌 حان وقت أذان {nextPrayer} - {ConvertTo12HourFormat(nextPrayerTime)}";
        }
        else
        {
            PrayerNotification = $"🕌 أذان {nextPrayer} القادم: {ConvertTo12HourFormat(nextPrayerTime)}";
        }
    }

    private Dictionary<string, TimeSpan> GetPrayerTimesForBish(DateTime date)
    {
        // استخدم الأوقات المحفوظة في الكاش إذا كانت متوفرة
        if (_cachedPrayerTimes != null)
        {
            return _cachedPrayerTimes;
        }

        // محاولة جلب أوقات الصلاة من الموقع مرة واحدة فقط
        var onlineTimes = Task.Run(async () => await FetchPrayerTimesFromWebsite()).Result;
        if (onlineTimes != null)
        {
            _cachedPrayerTimes = onlineTimes;
            return onlineTimes;
        }

        // في حالة فشل الاتصال، استخدم الأوقات المحلية المحفوظة
        // أوقات الصلاة الدقيقة لمحافظة بيش - المملكة العربية السعودية
        // إحداثيات بيش: خط العرض 17.7333°, خط الطول 42.6000°
        // المنطقة الزمنية: UTC+3 (توقيت السعودية)

        var month = date.Month;

        // أوقات الصلاة الدقيقة حسب الشهر لمحافظة بيش (احتياطية)
        return month switch
        {
            // يناير - شتاء
            1 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 50, 0) },
                { "الشروق", new TimeSpan(7, 10, 0) },
                { "الظهر", new TimeSpan(12, 15, 0) },
                { "العصر", new TimeSpan(15, 25, 0) },
                { "المغرب", new TimeSpan(17, 50, 0) },
                { "العشاء", new TimeSpan(19, 20, 0) }
            },
            // فبراير - شتاء
            2 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 45, 0) },
                { "الشروق", new TimeSpan(7, 0, 0) },
                { "الظهر", new TimeSpan(12, 18, 0) },
                { "العصر", new TimeSpan(15, 35, 0) },
                { "المغرب", new TimeSpan(18, 5, 0) },
                { "العشاء", new TimeSpan(19, 35, 0) }
            },
            // مارس - ربيع
            3 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 30, 0) },
                { "الشروق", new TimeSpan(6, 45, 0) },
                { "الظهر", new TimeSpan(12, 20, 0) },
                { "العصر", new TimeSpan(15, 45, 0) },
                { "المغرب", new TimeSpan(18, 20, 0) },
                { "العشاء", new TimeSpan(19, 50, 0) }
            },
            // أبريل - ربيع
            4 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 15, 0) },
                { "الشروق", new TimeSpan(6, 25, 0) },
                { "الظهر", new TimeSpan(12, 18, 0) },
                { "العصر", new TimeSpan(15, 50, 0) },
                { "المغرب", new TimeSpan(18, 35, 0) },
                { "العشاء", new TimeSpan(20, 5, 0) }
            },
            // مايو - صيف
            5 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(4, 55, 0) },
                { "الشروق", new TimeSpan(6, 10, 0) },
                { "الظهر", new TimeSpan(12, 15, 0) },
                { "العصر", new TimeSpan(15, 55, 0) },
                { "المغرب", new TimeSpan(18, 50, 0) },
                { "العشاء", new TimeSpan(20, 20, 0) }
            },
            // يونيو - صيف
            6 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(4, 45, 0) },
                { "الشروق", new TimeSpan(6, 5, 0) },
                { "الظهر", new TimeSpan(12, 15, 0) },
                { "العصر", new TimeSpan(16, 0, 0) },
                { "المغرب", new TimeSpan(19, 0, 0) },
                { "العشاء", new TimeSpan(20, 30, 0) }
            },
            // يوليو - صيف
            7 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(4, 50, 0) },
                { "الشروق", new TimeSpan(6, 10, 0) },
                { "الظهر", new TimeSpan(12, 18, 0) },
                { "العصر", new TimeSpan(16, 0, 0) },
                { "المغرب", new TimeSpan(19, 5, 0) },
                { "العشاء", new TimeSpan(20, 35, 0) }
            },
            // أغسطس - صيف
            8 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 0, 0) },
                { "الشروق", new TimeSpan(6, 15, 0) },
                { "الظهر", new TimeSpan(12, 18, 0) },
                { "العصر", new TimeSpan(15, 55, 0) },
                { "المغرب", new TimeSpan(18, 55, 0) },
                { "العشاء", new TimeSpan(20, 25, 0) }
            },
            // سبتمبر - خريف
            9 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 10, 0) },
                { "الشروق", new TimeSpan(6, 25, 0) },
                { "الظهر", new TimeSpan(12, 15, 0) },
                { "العصر", new TimeSpan(15, 45, 0) },
                { "المغرب", new TimeSpan(18, 35, 0) },
                { "العشاء", new TimeSpan(20, 5, 0) }
            },
            // أكتوبر - خريف
            10 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 20, 0) },
                { "الشروق", new TimeSpan(6, 35, 0) },
                { "الظهر", new TimeSpan(12, 12, 0) },
                { "العصر", new TimeSpan(15, 30, 0) },
                { "المغرب", new TimeSpan(18, 15, 0) },
                { "العشاء", new TimeSpan(19, 45, 0) }
            },
            // نوفمبر - خريف
            11 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 35, 0) },
                { "الشروق", new TimeSpan(6, 50, 0) },
                { "الظهر", new TimeSpan(12, 12, 0) },
                { "العصر", new TimeSpan(15, 20, 0) },
                { "المغرب", new TimeSpan(17, 55, 0) },
                { "العشاء", new TimeSpan(19, 25, 0) }
            },
            // ديسمبر - شتاء
            12 => new Dictionary<string, TimeSpan>
            {
                { "الفجر", new TimeSpan(5, 45, 0) },
                { "الشروق", new TimeSpan(7, 5, 0) },
                { "الظهر", new TimeSpan(12, 15, 0) },
                { "العصر", new TimeSpan(15, 20, 0) },
                { "المغرب", new TimeSpan(17, 50, 0) },
                { "العشاء", new TimeSpan(19, 20, 0) }
            },
            _ => new Dictionary<string, TimeSpan>() // افتراضي
        };
    }

    private string ConvertTo12HourFormat(TimeSpan time)
    {
        var hour = time.Hours;
        var minute = time.Minutes;

        // التحويل إلى نظام 12 ساعة مع "ص" و "م"
        if (hour == 0)
        {
            return $"12:{minute:D2} ص";
        }
        else if (hour < 12)
        {
            return $"{hour}:{minute:D2} ص";
        }
        else if (hour == 12)
        {
            return $"12:{minute:D2} م";
        }
        else
        {
            var displayHour = hour - 12;
            return $"{displayHour}:{minute:D2} م";
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;
    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    private async Task<Dictionary<string, TimeSpan>?> FetchPrayerTimesFromWebsite()
    {
        try
        {
            var response = await httpClient.GetStringAsync("https://dirarab.net/prayertimes/Saudi-Arabia/Bisa");

            var prayerTimes = new Dictionary<string, TimeSpan>();

            // استخراج أوقات الصلاة باستخدام Regular Expressions
            var patterns = new Dictionary<string, string>
            {
                { "الفجر", @"🕐 الفجر : (\d{1,2}):(\d{2})" },
                { "الشروق", @"🕐 الشروق : (\d{1,2}):(\d{2})" },
                { "الظهر", @"🕐 الظهر : (\d{1,2}):(\d{2})" },
                { "العصر", @"🕐 العصر : (\d{1,2}):(\d{2})" },
                { "المغرب", @"🕐 المغرب : (\d{1,2}):(\d{2})" },
                { "العشاء", @"🕐 العشاء : (\d{1,2}):(\d{2})" }
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(response, pattern.Value);
                if (match.Success)
                {
                    var hour = int.Parse(match.Groups[1].Value);
                    var minute = int.Parse(match.Groups[2].Value);

                    // تصحيح الأوقات بناءً على طبيعة الصلاة
                    // الموقع يعرض أوقات مختصرة، نحتاج لتحويلها للنظام الصحيح
                    if (pattern.Key == "العصر")
                    {
                        if (hour < 12)
                        {
                            hour += 12; // العصر دائماً بعد الظهر
                        }
                    }
                    else if (pattern.Key == "المغرب")
                    {
                        if (hour < 12)
                        {
                            hour += 12; // المغرب دائماً مساءً
                        }
                    }
                    else if (pattern.Key == "العشاء")
                    {
                        if (hour < 12)
                        {
                            hour += 12; // العشاء دائماً مساءً
                        }
                    }

                    prayerTimes[pattern.Key] = new TimeSpan(hour, minute, 0);
                }
            }

            return prayerTimes.Count == 6 ? prayerTimes : null;
        }
        catch (Exception ex)
        {
            // في حالة فشل الاتصال، استخدم الأوقات المحلية
            System.Diagnostics.Debug.WriteLine($"خطأ في جلب أوقات الصلاة: {ex.Message}");
            return null;
        }
    }

    private void InitializeData()
    {
        // بيانات الطلبات
        Orders.Add(new OrderData { Date = "2023/9/12", Amount = "3,500.00 ريال", Service = "خدمة تجارية", Client = "علي محمد", QueryType = "استعلام", Product = "visa", Status = "مكتملة" });
        Orders.Add(new OrderData { Date = "2023/9/11", Amount = "2,750.00 ريال", Service = "خدمة شخصية", Client = "فاطمة أحمد", QueryType = "استعلام", Product = "visa", Status = "قيد المعالجة" });
        Orders.Add(new OrderData { Date = "2023/9/10", Amount = "1,200.00 ريال", Service = "خدمة تجارية", Client = "محمد عبدالله", QueryType = "استعلام", Product = "other", Status = "مكتملة" });
        Orders.Add(new OrderData { Date = "2023/9/9", Amount = "2,940.00 ريال", Service = "خدمة شخصية", Client = "سارة خالد", QueryType = "استعلام", Product = "visa", Status = "مكتملة" });
        Orders.Add(new OrderData { Date = "2023/9/8", Amount = "2,940.00 ريال", Service = "خدمة تجارية", Client = "أحمد محمد", QueryType = "استعلام", Product = "visa", Status = "قيد المعالجة" });

        // بيانات الطلبات المعلقة
        PendingOrders.Add(new PendingOrderData { ClientName = "محمد الأحمد", OrderDate = "2023/6/1", ServiceType = "خدمة تجارية", RequiredAmount = "500.00 ريال", OrderTime = "14:30", Status = "قيد المراجعة" });
        PendingOrders.Add(new PendingOrderData { ClientName = "فاطمة علي", OrderDate = "2023/6/1", ServiceType = "خدمة شخصية", RequiredAmount = "750.00 ريال", OrderTime = "15:45", Status = "في الانتظار" });
        PendingOrders.Add(new PendingOrderData { ClientName = "عبدالله سالم", OrderDate = "2023/6/1", ServiceType = "خدمة تجارية", RequiredAmount = "1,200.00 ريال", OrderTime = "16:20", Status = "قيد المراجعة" });
        PendingOrders.Add(new PendingOrderData { ClientName = "نورا محمد", OrderDate = "2023/6/1", ServiceType = "خدمة شخصية", RequiredAmount = "300.00 ريال", OrderTime = "17:10", Status = "في الانتظار" });
        PendingOrders.Add(new PendingOrderData { ClientName = "خالد أحمد", OrderDate = "2023/6/1", ServiceType = "خدمة تجارية", RequiredAmount = "950.00 ريال", OrderTime = "18:30", Status = "قيد المراجعة" });
    }

    private void LoadDataIntoGrids()
    {
        // ربط البيانات عبر DataContext للعمل مع DevExpress GridControl
        this.DataContext = this;
    }

    // معالجات أحداث النقر على البطاقات التفاعلية
    private void RevenueCard_Click(object sender, MouseButtonEventArgs e)
    {
        try
        {
            // فتح نافذة المعاملات لعرض مصدر الإيرادات
            var transactionsWindow = new SimpleTransactionsWindow();
            transactionsWindow.Show();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في فتح نافذة المعاملات من RevenueCard: {ex.Message}");
            System.Windows.MessageBox.Show($"خطأ في فتح نافذة المعاملات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void WorkersCard_Click(object sender, MouseButtonEventArgs e)
    {
        try
        {
            // تحديث الإحصائيات أولاً
            await RefreshStatisticsFromDatabase();

            // فتح صفحة العمال
            var workersWindow = new WorkersWindow();
            workersWindow.Show();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في فتح صفحة العمال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ObligationsCard_Click(object sender, MouseButtonEventArgs e)
    {
        try
        {
            // فتح صفحة الالتزامات (سيتم تطويرها لاحقاً)
            System.Windows.MessageBox.Show("صفحة الالتزامات قيد التطوير 🚧\nسيتم ربطها بقاعدة البيانات قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void TransactionsCard_Click(object sender, MouseButtonEventArgs e)
    {
        try
        {
            // تحديث الإحصائيات أولاً
            await RefreshStatisticsFromDatabase();

            // فتح نافذة المعاملات لعرض إجمالي المعاملات
            var transactionsWindow = new SimpleTransactionsWindow();
            transactionsWindow.Show();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }


}

// فئات البيانات
public class OrderData
{
    public string Date { get; set; } = "";
    public string Amount { get; set; } = "";
    public string Service { get; set; } = "";
    public string Client { get; set; } = "";
    public string QueryType { get; set; } = "";
    public string Product { get; set; } = "";
    public string Status { get; set; } = "";
}

public class PendingOrderData
{
    public string ClientName { get; set; } = "";
    public string OrderDate { get; set; } = "";
    public string ServiceType { get; set; } = "";
    public string RequiredAmount { get; set; } = "";
    public string OrderTime { get; set; } = "";
    public string Status { get; set; } = "";
}