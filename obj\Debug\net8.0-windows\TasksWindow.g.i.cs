﻿#pragma checksum "..\..\..\TasksWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3C2F052AE6D7EC251B28A7F125AAE2C70C394057"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// TasksWindow
    /// </summary>
    public partial class TasksWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 117 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalTasks;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNewTasks;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInProgressTasks;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCompletedTasks;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddTask;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEditTask;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteTask;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshData;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\TasksWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridTasks;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/taskswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\TasksWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTotalTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtNewTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtInProgressTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtCompletedTasks = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BtnAddTask = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\TasksWindow.xaml"
            this.BtnAddTask.Click += new System.Windows.RoutedEventHandler(this.BtnAddTask_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnEditTask = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\TasksWindow.xaml"
            this.BtnEditTask.Click += new System.Windows.RoutedEventHandler(this.BtnEditTask_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnDeleteTask = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\TasksWindow.xaml"
            this.BtnDeleteTask.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteTask_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnRefreshData = ((System.Windows.Controls.Button)(target));
            
            #line 204 "..\..\..\TasksWindow.xaml"
            this.BtnRefreshData.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.GridTasks = ((DevExpress.Xpf.Grid.GridControl)(target));
            
            #line 221 "..\..\..\TasksWindow.xaml"
            this.GridTasks.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.GridTasks_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

