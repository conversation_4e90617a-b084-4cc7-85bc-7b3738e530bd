using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    public class TaskItem
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = "";

        [StringLength(1000)]
        public string Description { get; set; } = "";

        [Required]
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        [Required]
        public TaskStatus Status { get; set; } = TaskStatus.New;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? StartDate { get; set; }

        public DateTime? DueDate { get; set; }

        public DateTime? CompletedDate { get; set; }

        [StringLength(100)]
        public string AssignedTo { get; set; } = "";

        [StringLength(100)]
        public string CreatedBy { get; set; } = "";

        [StringLength(500)]
        public string Notes { get; set; } = "";

        [StringLength(200)]
        public string ClientName { get; set; } = "";

        [StringLength(100)]
        public string TransactionType { get; set; } = "";

        [StringLength(100)]
        public string TaskType { get; set; } = "";

        public bool IsNotificationEnabled { get; set; } = true;

        public DateTime? LastNotificationDate { get; set; }

        // نسبة التقدم من 0 إلى 100
        public int Progress { get; set; } = 0;

        // خصائص محسوبة
        public string StatusText => Status switch
        {
            TaskStatus.New => "جديدة",
            TaskStatus.InProgress => "قيد التنفيذ",
            TaskStatus.Completed => "مكتملة",
            TaskStatus.Cancelled => "ملغية",
            TaskStatus.OnHold => "معلقة",
            _ => "غير محدد"
        };

        public string PriorityText => Priority switch
        {
            TaskPriority.Low => "منخفضة",
            TaskPriority.Medium => "متوسطة",
            TaskPriority.High => "عالية",
            TaskPriority.Critical => "حرجة",
            _ => "غير محدد"
        };

        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && Status != TaskStatus.Completed;

        public bool IsInProgress => Status == TaskStatus.InProgress;

        public bool IsCompleted => Status == TaskStatus.Completed;

        public int DaysRemaining
        {
            get
            {
                if (!DueDate.HasValue || Status == TaskStatus.Completed)
                    return 0;
                
                var days = (DueDate.Value - DateTime.Now).Days;
                return days > 0 ? days : 0;
            }
        }

        public string ProgressText
        {
            get
            {
                if (IsCompleted)
                    return "مكتملة";

                if (IsOverdue)
                    return "متأخرة";

                if (DaysRemaining > 0)
                    return $"باقي {DaysRemaining} يوم";

                return "اليوم";
            }
        }

        public string ReminderText
        {
            get
            {
                return IsNotificationEnabled ? "مفعل" : "معطل";
            }
        }
    }

    public enum TaskStatus
    {
        New = 0,        // جديدة
        InProgress = 1, // قيد التنفيذ
        Completed = 2,  // مكتملة
        Cancelled = 3,  // ملغية
        OnHold = 4      // معلقة
    }

    public enum TaskPriority
    {
        Low = 0,      // منخفضة
        Medium = 1,   // متوسطة
        High = 2,     // عالية
        Critical = 3  // حرجة
    }
}
