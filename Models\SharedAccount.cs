using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    public class SharedAccount
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string WorkerName { get; set; } = string.Empty;
        
        [Required]
        public DateTime TransactionDate { get; set; }
        
        // المبالغ المختلفة
        public decimal SponsorAmount { get; set; } = 0;
        public decimal OfficeAmount { get; set; } = 0;
        public decimal PassportFees { get; set; } = 0;
        public decimal WorkPermitFees { get; set; } = 0;
        public decimal RecruitmentAmount { get; set; } = 0;
        public decimal ServiceFees { get; set; } = 0;
        public decimal MedicalInsurance { get; set; } = 0; // مبلغ التأمين الطبي
        public decimal TransferFees { get; set; } = 0; // رسوم النقل

        // نوع الخدمة: نقل خدمات أو إصدار تأشيرة
        [StringLength(100)]
        public string ServiceCategory { get; set; } = "نقل خدمات"; // نقل خدمات، إصدار تأشيرة

        [StringLength(100)]
        public string ServiceType { get; set; } = "مهني"; // مهني أو عمالة منزلية

        // معلومات العميل (للتأشيرات)
        [StringLength(200)]
        public string ClientName { get; set; } = string.Empty;

        [StringLength(100)]
        public string VisaType { get; set; } = "مهني"; // مهني أو منزلي

        // رسوم إصدار التأشيرات
        public decimal GovernmentFees { get; set; } = 0; // رسوم الحكومة
        public decimal ForeignAffairsFees { get; set; } = 0; // رسوم تفويض الخارجية
        public decimal ChamberOfCommerceFees { get; set; } = 0; // تصديق الغرفة التجارية
        public decimal EmploymentFees { get; set; } = 0; // رسوم الحكومة توظيف (للمنزلي)

        // خيارات الضريبة والحسم
        public bool EnableTax { get; set; } = false; // تفعيل الضريبة
        public bool EnableDiscount { get; set; } = false; // تفعيل الحسم
        public decimal TaxPercentage { get; set; } = 15; // نسبة الضريبة (15% افتراضياً)
        public decimal DiscountAmount { get; set; } = 0; // مبلغ الحسم
        public decimal TaxAmount { get; set; } = 0; // مبلغ الضريبة

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // الحقول الجديدة المطلوبة
        public decimal TotalGrossAmount { get; set; } // المبلغ الإجمالي
        public decimal TotalExpenses { get; set; } // إجمالي المصروفات
        public decimal NetProfit { get; set; } // صافي الربح
        public decimal ProfitShare { get; set; } // حصة الربح (صافي الربح / 2)

        // خصائص محسوبة
        public decimal TotalAmount => SponsorAmount + OfficeAmount + PassportFees + WorkPermitFees + RecruitmentAmount + ServiceFees + MedicalInsurance + TransferFees;
        public string TotalAmountFormatted => TotalAmount.ToString("N0") + " ريال";
        public string TransactionDateFormatted => TransactionDate.ToString("yyyy/MM/dd");
        public string SponsorAmountFormatted => SponsorAmount.ToString("N0") + " ريال";
        public string OfficeAmountFormatted => OfficeAmount.ToString("N0") + " ريال";
        public string PassportFeesFormatted => PassportFees.ToString("N0") + " ريال";
        public string WorkPermitFeesFormatted => WorkPermitFees.ToString("N0") + " ريال";
        public string RecruitmentAmountFormatted => RecruitmentAmount.ToString("N0") + " ريال";
        public string ServiceFeesFormatted => ServiceFees.ToString("N0") + " ريال";
        public string MedicalInsuranceFormatted => MedicalInsurance.ToString("N0") + " ريال";
        public string TransferFeesFormatted => TransferFees.ToString("N0") + " ريال";

        // خصائص منسقة للحقول الجديدة
        public string TotalGrossAmountFormatted => TotalGrossAmount.ToString("N0") + " ريال";
        public string TotalExpensesFormatted => TotalExpenses.ToString("N0") + " ريال";
        public string NetProfitFormatted => NetProfit.ToString("N0") + " ريال";
        public string ProfitShareFormatted => ProfitShare.ToString("N0") + " ريال";

        // أيقونة حسب نوع المعاملة
        public string TransactionIcon => "💼";
        public string StatusColor => "#3B82F6"; // أزرق

        // تفاصيل الحساب
        public string AccountSummary => $"إجمالي: {TotalGrossAmountFormatted} | صافي الربح: {NetProfitFormatted} | العامل: {WorkerName}";
    }
}
