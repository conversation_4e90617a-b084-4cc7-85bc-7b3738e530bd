using System;
using System.Windows;
using DevExpress.Xpf.Core;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using System.Diagnostics;

namespace ArabicDashboard
{
    public partial class AddEditCommitmentWindow : ThemedWindow
    {
        private readonly DatabaseService _databaseService;
        private readonly Commitment? _commitment;
        private readonly bool _isEditMode;

        public AddEditCommitmentWindow(Commitment? commitment = null)
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _commitment = commitment;
            _isEditMode = commitment != null;

            if (_isEditMode)
            {
                TitleTextBlock.Text = "تعديل الالتزام";
                LoadCommitmentData();
            }
            else
            {
                // تعيين التاريخ الافتراضي لتاريخ الاستحقاق
                DueDatePicker.DateTime = DateTime.Today.AddDays(30);
            }
        }

        private void LoadCommitmentData()
        {
            if (_commitment == null) return;

            try
            {
                CommitmentNameTextBox.Text = _commitment.CommitmentName;
                CommitmentTypeCombo.Text = _commitment.CommitmentType;
                DueDatePicker.DateTime = _commitment.DueDate;
                CategoryCombo.Text = _commitment.CommitmentCategory;
                AmountSpinEdit.Value = (double)_commitment.Amount;
                DescriptionTextBox.Text = _commitment.Description;
                NotesTextBox.Text = _commitment.Notes;
                NotificationCheckBox.IsChecked = _commitment.IsNotificationEnabled;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحميل بيانات الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل بيانات الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                Commitment commitment;

                if (_isEditMode && _commitment != null)
                {
                    // تحديث الالتزام الموجود
                    commitment = _commitment;
                    commitment.UpdatedDate = DateTime.Now;
                }
                else
                {
                    // إنشاء التزام جديد
                    commitment = new Commitment
                    {
                        CreatedDate = DateTime.Now,
                        CreatedBy = "المستخدم",
                        IsActive = true,
                        Status = "مستحق"
                    };
                }

                // تعيين البيانات
                commitment.CommitmentName = CommitmentNameTextBox.Text.Trim();
                commitment.CommitmentType = CommitmentTypeCombo.Text.Trim();
                commitment.DueDate = DueDatePicker.DateTime ?? DateTime.Today;
                commitment.CommitmentCategory = CategoryCombo.Text;
                commitment.Amount = Convert.ToDecimal(AmountSpinEdit.Value ?? 0);
                commitment.Description = DescriptionTextBox.Text?.Trim() ?? "";
                commitment.Notes = NotesTextBox.Text?.Trim() ?? "";
                commitment.IsNotificationEnabled = NotificationCheckBox.IsChecked ?? true;

                // حفظ في قاعدة البيانات
                if (_isEditMode)
                {
                    await _databaseService.UpdateCommitmentAsync(commitment);
                    System.Windows.MessageBox.Show("تم تحديث الالتزام بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    await _databaseService.AddCommitmentAsync(commitment);
                    System.Windows.MessageBox.Show("تم إضافة الالتزام بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في حفظ الالتزام: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في حفظ الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم الالتزام
            if (string.IsNullOrWhiteSpace(CommitmentNameTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال اسم الالتزام", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CommitmentNameTextBox.Focus();
                return false;
            }

            // التحقق من نوع الالتزام
            if (string.IsNullOrWhiteSpace(CommitmentTypeCombo.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال نوع الالتزام", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CommitmentTypeCombo.Focus();
                return false;
            }

            // التحقق من تاريخ الاستحقاق
            if (DueDatePicker.DateTime == null)
            {
                System.Windows.MessageBox.Show("يرجى تحديد تاريخ الاستحقاق", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DueDatePicker.Focus();
                return false;
            }

            // التحقق من التصنيف
            if (string.IsNullOrWhiteSpace(CategoryCombo.Text))
            {
                System.Windows.MessageBox.Show("يرجى اختيار تصنيف الالتزام", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryCombo.Focus();
                return false;
            }

            // التحقق من المبلغ (اختياري ولكن يجب أن يكون موجب إذا تم إدخاله)
            if (AmountSpinEdit.Value < 0)
            {
                System.Windows.MessageBox.Show("المبلغ يجب أن يكون أكبر من أو يساوي صفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountSpinEdit.Focus();
                return false;
            }

            return true;
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
