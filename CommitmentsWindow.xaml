<dx:ThemedWindow x:Class="ArabicDashboard.CommitmentsWindow"
                  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
                  xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
                  xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
                  Title="📋 إدارة الالتزامات"
                  Height="600" Width="900"
                  WindowState="Maximized"
                  WindowStartupLocation="CenterScreen"
                  FlowDirection="RightToLeft"
                  FontFamily="Segoe UI"
                  FontSize="14">
    
    <Grid Background="#F8FAFC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان والأدوات -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الالتزامات" FontSize="20" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="متابعة الالتزامات المالية والشخصية مع التنبيهات الذكية" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                    <dx:SimpleButton x:Name="AddCommitmentBtn" 
                                      Content="➕ إضافة التزام جديد" 
                                      Click="AddCommitmentBtn_Click"
                                      Margin="0,0,10,0"
                                      Padding="15,8"
                                      Background="#3B82F6"
                                      Foreground="White"
                                      BorderThickness="0"/>
                    
                    <dx:SimpleButton x:Name="RefreshBtn" 
                                      Content="🔄 تحديث" 
                                      Click="RefreshBtn_Click"
                                      Margin="0,0,0,0"
                                      Padding="15,8"
                                      Background="#10B981"
                                      Foreground="White"
                                      BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- جدول البيانات -->
        <dxg:GridControl x:Name="CommitmentsGrid" 
                          Grid.Row="2" 
                          Margin="20"
                          Background="White"
                          BorderBrush="#E5E7EB"
                          BorderThickness="1">
            
            <dxg:GridControl.View>
                <dxg:TableView x:Name="CommitmentsTableView"
                               ShowGroupPanel="False"
                               AllowEditing="False"
                               AutoWidth="True"
                               NavigationStyle="Row"
                               ShowIndicator="True"
                               ShowVerticalLines="False"
                               ShowHorizontalLines="True"
                               AlternateRowBackground="#F9FAFB">
                </dxg:TableView>
            </dxg:GridControl.View>
            
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="StatusIcon" Header="الحالة" Width="60"/>
                <dxg:GridColumn FieldName="CommitmentName" Header="اسم الالتزام" Width="200"/>
                <dxg:GridColumn FieldName="CommitmentType" Header="نوع الالتزام" Width="150"/>
                <dxg:GridColumn FieldName="CommitmentCategory" Header="التصنيف" Width="100"/>
                <dxg:GridColumn FieldName="FormattedDueDate" Header="تاريخ الاستحقاق" Width="120"/>
                <dxg:GridColumn FieldName="DaysRemainingText" Header="المتبقي" Width="120"/>
                <dxg:GridColumn FieldName="FormattedAmount" Header="المبلغ" Width="120"/>
                <dxg:GridColumn FieldName="Status" Header="الحالة" Width="100"/>
                <dxg:GridColumn FieldName="Description" Header="الوصف" Width="200"/>
            </dxg:GridControl.Columns>
        </dxg:GridControl>
    </Grid>
</dx:ThemedWindow>
