﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files\DevExpress 24.1\Components\Offline Packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files\DevExpress 24.1\Components\Offline Packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.5\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\9.0.5\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.5\build\net8.0\Microsoft.EntityFrameworkCore.Design.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore.design\9.0.5\build\net8.0\Microsoft.EntityFrameworkCore.Design.props')" />
    <Import Project="C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.data.desktop\24.1.7\build\netcoreapp3.0\DevExpress.Data.Desktop.props" Condition="Exists('C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.data.desktop\24.1.7\build\netcoreapp3.0\DevExpress.Data.Desktop.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Web_WebView2 Condition=" '$(PkgMicrosoft_Web_WebView2)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2210.55</PkgMicrosoft_Web_WebView2>
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.data\24.1.7</PkgDevExpress_Data>
    <PkgDevExpress_Xpo Condition=" '$(PkgDevExpress_Xpo)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.xpo\24.1.7</PkgDevExpress_Xpo>
    <PkgDevExpress_Wpf_Core Condition=" '$(PkgDevExpress_Wpf_Core)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.core\24.1.7</PkgDevExpress_Wpf_Core>
    <PkgDevExpress_Wpf_Ribbon Condition=" '$(PkgDevExpress_Wpf_Ribbon)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.ribbon\24.1.7</PkgDevExpress_Wpf_Ribbon>
    <PkgDevExpress_Wpf_LayoutControl Condition=" '$(PkgDevExpress_Wpf_LayoutControl)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.layoutcontrol\24.1.7</PkgDevExpress_Wpf_LayoutControl>
    <PkgDevExpress_Wpf_Grid_Core Condition=" '$(PkgDevExpress_Wpf_Grid_Core)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.grid.core\24.1.7</PkgDevExpress_Wpf_Grid_Core>
    <PkgDevExpress_Wpf_Controls Condition=" '$(PkgDevExpress_Wpf_Controls)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.controls\24.1.7</PkgDevExpress_Wpf_Controls>
    <PkgDevExpress_Wpf_DocumentViewer_Core Condition=" '$(PkgDevExpress_Wpf_DocumentViewer_Core)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.documentviewer.core\24.1.7</PkgDevExpress_Wpf_DocumentViewer_Core>
    <PkgDevExpress_Wpf_Docking Condition=" '$(PkgDevExpress_Wpf_Docking)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.docking\24.1.7</PkgDevExpress_Wpf_Docking>
    <PkgDevExpress_Wpf_Printing Condition=" '$(PkgDevExpress_Wpf_Printing)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.printing\24.1.7</PkgDevExpress_Wpf_Printing>
    <PkgDevExpress_Wpf_RichEdit Condition=" '$(PkgDevExpress_Wpf_RichEdit)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.richedit\24.1.7</PkgDevExpress_Wpf_RichEdit>
    <PkgDevExpress_Wpf_PropertyGrid Condition=" '$(PkgDevExpress_Wpf_PropertyGrid)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.propertygrid\24.1.7</PkgDevExpress_Wpf_PropertyGrid>
    <PkgDevExpress_Wpf_Grid_Printing Condition=" '$(PkgDevExpress_Wpf_Grid_Printing)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.grid.printing\24.1.7</PkgDevExpress_Wpf_Grid_Printing>
    <PkgDevExpress_DataAccess Condition=" '$(PkgDevExpress_DataAccess)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.dataaccess\24.1.7</PkgDevExpress_DataAccess>
    <PkgDevExpress_Wpf_ExpressionEditor Condition=" '$(PkgDevExpress_Wpf_ExpressionEditor)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.expressioneditor\24.1.7</PkgDevExpress_Wpf_ExpressionEditor>
    <PkgDevExpress_Wpf_Grid Condition=" '$(PkgDevExpress_Wpf_Grid)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.grid\24.1.7</PkgDevExpress_Wpf_Grid>
    <PkgDevExpress_Wpf_Charts Condition=" '$(PkgDevExpress_Wpf_Charts)' == '' ">C:\Program Files\DevExpress 24.1\Components\Offline Packages\devexpress.wpf.charts\24.1.7</PkgDevExpress_Wpf_Charts>
  </PropertyGroup>
</Project>