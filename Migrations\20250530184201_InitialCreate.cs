﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArabicDashboard.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgentTransactions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AgentName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ServiceType = table.Column<string>(type: "TEXT", maxLength: 300, nullable: false),
                    ServiceFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NetProfit = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    TransferStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PersonalCommitments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CommitmentName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CommitmentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", nullable: false),
                    Duration = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalCommitments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Residences",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    WorkerName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ResidenceNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    RenewalDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    MedicalInsuranceExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Residences", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SharedAccounts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AccountName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    WorkerName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    SponsorAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OfficeAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PassportFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    WorkPermitFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RecruitmentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ServiceFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MedicalInsurance = table.Column<decimal>(type: "TEXT", nullable: false),
                    ServiceType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    TotalGrossAmount = table.Column<decimal>(type: "TEXT", nullable: false),
                    TotalExpenses = table.Column<decimal>(type: "TEXT", nullable: false),
                    NetProfit = table.Column<decimal>(type: "TEXT", nullable: false),
                    ProfitShare = table.Column<decimal>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SharedAccounts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SimpleTransactions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ClientName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ServiceType = table.Column<string>(type: "TEXT", maxLength: 300, nullable: false),
                    ServiceFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    GovernmentFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    LaborOfficeFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NetProfit = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    IsEmployed = table.Column<bool>(type: "INTEGER", nullable: false),
                    EmploymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SimpleTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Workers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    WorkerName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ResidenceNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    EntryDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    SubscriptionEndDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    SponsorName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    SponsorIdNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    SponsorIdImagePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CommercialRegistrationPath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    QawaUsername = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    QawaPassword = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    AbsherUsername = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    AbsherPassword = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ChamberUsername = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ChamberPassword = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Workers", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentTransactions");

            migrationBuilder.DropTable(
                name: "PersonalCommitments");

            migrationBuilder.DropTable(
                name: "Residences");

            migrationBuilder.DropTable(
                name: "SharedAccounts");

            migrationBuilder.DropTable(
                name: "SimpleTransactions");

            migrationBuilder.DropTable(
                name: "Workers");
        }
    }
}
