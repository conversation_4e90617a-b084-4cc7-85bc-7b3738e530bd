{"version": 2, "dgSpecHash": "ZuQnnb9sFJ0=", "success": true, "projectFilePath": "E:\\ArabicDashboard_backup\\ArabicDashboard.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.6.1\\bouncycastle.cryptography.2.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.netcore\\2.2.1\\bouncycastle.netcore.2.2.1.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.charts.core\\24.1.7\\devexpress.charts.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.codeparser\\24.1.7\\devexpress.codeparser.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.data\\24.1.7\\devexpress.data.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.data.desktop\\24.1.7\\devexpress.data.desktop.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.dataaccess\\24.1.7\\devexpress.dataaccess.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.datavisualization.core\\24.1.7\\devexpress.datavisualization.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.drawing\\24.1.7\\devexpress.drawing.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.images\\24.1.7\\devexpress.images.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.mvvm\\24.1.7\\devexpress.mvvm.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.office.core\\24.1.7\\devexpress.office.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.pdf.core\\24.1.7\\devexpress.pdf.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.pdf.drawing\\24.1.7\\devexpress.pdf.drawing.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.printing.core\\24.1.7\\devexpress.printing.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.richedit.core\\24.1.7\\devexpress.richedit.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.charts\\24.1.7\\devexpress.wpf.charts.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.controls\\24.1.7\\devexpress.wpf.controls.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.core\\24.1.7\\devexpress.wpf.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.docking\\24.1.7\\devexpress.wpf.docking.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.documentviewer.core\\24.1.7\\devexpress.wpf.documentviewer.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.expressioneditor\\24.1.7\\devexpress.wpf.expressioneditor.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.grid\\24.1.7\\devexpress.wpf.grid.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.grid.core\\24.1.7\\devexpress.wpf.grid.core.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.grid.printing\\24.1.7\\devexpress.wpf.grid.printing.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.layoutcontrol\\24.1.7\\devexpress.wpf.layoutcontrol.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.office\\24.1.7\\devexpress.wpf.office.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.printing\\24.1.7\\devexpress.wpf.printing.24.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devexpress.wpf.projecttemplates\\25.1.1-alpha\\devexpress.wpf.projecttemplates.25.1.1-alpha.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.propertygrid\\24.1.7\\devexpress.wpf.propertygrid.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.ribbon\\24.1.7\\devexpress.wpf.ribbon.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.richedit\\24.1.7\\devexpress.wpf.richedit.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.all\\24.1.7\\devexpress.wpf.themes.all.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.dxstyle\\24.1.7\\devexpress.wpf.themes.dxstyle.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.lightgray\\24.1.7\\devexpress.wpf.themes.lightgray.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.metropolisdark\\24.1.7\\devexpress.wpf.themes.metropolisdark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.metropolislight\\24.1.7\\devexpress.wpf.themes.metropolislight.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2007black\\24.1.7\\devexpress.wpf.themes.office2007black.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2007blue\\24.1.7\\devexpress.wpf.themes.office2007blue.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2007silver\\24.1.7\\devexpress.wpf.themes.office2007silver.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2010black\\24.1.7\\devexpress.wpf.themes.office2010black.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2010blue\\24.1.7\\devexpress.wpf.themes.office2010blue.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2010silver\\24.1.7\\devexpress.wpf.themes.office2010silver.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2013\\24.1.7\\devexpress.wpf.themes.office2013.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2013darkgray\\24.1.7\\devexpress.wpf.themes.office2013darkgray.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2013lightgray\\24.1.7\\devexpress.wpf.themes.office2013lightgray.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016black\\24.1.7\\devexpress.wpf.themes.office2016black.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016blackse\\24.1.7\\devexpress.wpf.themes.office2016blackse.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016colorful\\24.1.7\\devexpress.wpf.themes.office2016colorful.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016colorfulse\\24.1.7\\devexpress.wpf.themes.office2016colorfulse.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016darkgrayse\\24.1.7\\devexpress.wpf.themes.office2016darkgrayse.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016white\\24.1.7\\devexpress.wpf.themes.office2016white.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2016whitese\\24.1.7\\devexpress.wpf.themes.office2016whitese.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2019black\\24.1.7\\devexpress.wpf.themes.office2019black.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2019colorful\\24.1.7\\devexpress.wpf.themes.office2019colorful.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2019darkgray\\24.1.7\\devexpress.wpf.themes.office2019darkgray.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2019highcontrast\\24.1.7\\devexpress.wpf.themes.office2019highcontrast.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.office2019white\\24.1.7\\devexpress.wpf.themes.office2019white.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.seven\\24.1.7\\devexpress.wpf.themes.seven.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.touchlinedark\\24.1.7\\devexpress.wpf.themes.touchlinedark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2010\\24.1.7\\devexpress.wpf.themes.vs2010.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017blue\\24.1.7\\devexpress.wpf.themes.vs2017blue.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017dark\\24.1.7\\devexpress.wpf.themes.vs2017dark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017light\\24.1.7\\devexpress.wpf.themes.vs2017light.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019blue\\24.1.7\\devexpress.wpf.themes.vs2019blue.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019dark\\24.1.7\\devexpress.wpf.themes.vs2019dark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019light\\24.1.7\\devexpress.wpf.themes.vs2019light.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.win10dark\\24.1.7\\devexpress.wpf.themes.win10dark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.win10light\\24.1.7\\devexpress.wpf.themes.win10light.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.win11dark\\24.1.7\\devexpress.wpf.themes.win11dark.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.wpf.themes.win11light\\24.1.7\\devexpress.wpf.themes.win11light.24.1.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages\\devexpress.xpo\\24.1.7\\devexpress.xpo.24.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\itext\\9.2.0\\itext.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\itext.commons\\9.2.0\\itext.commons.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\itext7\\9.2.0\\itext7.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\itextsharp.lgplv2.core\\3.7.4\\itextsharp.lgplv2.core.3.7.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\7.0.0\\microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.locator\\1.7.8\\microsoft.build.locator.1.7.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.msbuild\\4.8.0\\microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\9.0.5\\microsoft.data.sqlite.core.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dotnet.platformabstractions\\1.1.0\\microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.5\\microsoft.entityframeworkcore.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.5\\microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.5\\microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\9.0.5\\microsoft.entityframeworkcore.design.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.5\\microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite\\9.0.5\\microsoft.entityframeworkcore.sqlite.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.core\\9.0.5\\microsoft.entityframeworkcore.sqlite.core.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.5\\microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.5\\microsoft.extensions.caching.memory.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.5\\microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.5\\microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.5\\microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.5\\microsoft.extensions.dependencymodel.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.5\\microsoft.extensions.logging.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.5\\microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\6.0.16\\microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.5\\microsoft.extensions.options.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.5\\microsoft.extensions.primitives.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.web.webview2\\1.0.2210.55\\microsoft.web.webview2.1.0.2210.55.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.texttemplating\\3.0.0\\mono.texttemplating.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.119.0\\skiasharp.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\3.119.0\\skiasharp.nativeassets.macos.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\3.119.0\\skiasharp.nativeassets.win32.3.119.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.1.0\\system.appcontext.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.7.0\\system.configuration.configurationmanager.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.5\\system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.process\\4.3.0\\system.diagnostics.process.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.0\\system.drawing.common.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\6.0.1\\system.formats.asn1.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.5\\system.io.pipelines.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\7.0.0\\system.reflection.metadata.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.1.0\\system.reflection.typeextensions.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.0.0\\system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.formatters\\4.3.0\\system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\6.0.3\\system.security.cryptography.pkcs.6.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.7.0\\system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\6.2.0\\system.servicemodel.http.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.netframingbase\\6.2.0\\system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\6.2.0\\system.servicemodel.nettcp.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\6.2.0\\system.servicemodel.primitives.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.3.0\\system.text.encoding.codepages.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.5\\system.text.encodings.web.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.5\\system.text.json.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.threadpool\\4.3.0\\system.threading.threadpool.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512"], "logs": []}