using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace ArabicDashboard.Models
{
    /// <summary>
    /// نموذج الاستعلام والحساب المحدث
    /// </summary>
    public class InquiryCalculation
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم العامل
        /// </summary>
        [Required]
        [StringLength(200)]
        public string WorkerName { get; set; } = string.Empty;

        /// <summary>
        /// رقم الإقامة
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ResidenceNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ انتهاء الإقامة
        /// </summary>
        [Required]
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// نوع العامل (مهني أو منزلي)
        /// </summary>
        [Required]
        public WorkerType WorkerType { get; set; } = WorkerType.Professional;

        /// <summary>
        /// نوع الخدمة (تجديد أو نقل خدمات)
        /// </summary>
        [Required]
        public ServiceType ServiceType { get; set; } = ServiceType.Renewal;

        /// <summary>
        /// رسوم الجوازات
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal PassportFees { get; set; } = 0;

        /// <summary>
        /// رسوم رخصة العمل (للمهني فقط)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal WorkPermitFees { get; set; } = 0;

        /// <summary>
        /// التأمين الطبي (للمهني فقط)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal MedicalInsurance { get; set; } = 0;

        /// <summary>
        /// توظيف السعودي (للمهني فقط)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal SaudiEmployment { get; set; } = 0;

        /// <summary>
        /// رسوم مكتب العمل (للمنزلي فقط)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal LaborOfficeFees { get; set; } = 0;

        /// <summary>
        /// رسوم الخدمة (للمهني والمنزلي)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal ServiceFees { get; set; } = 0;

        /// <summary>
        /// رسوم نقل الخدمات (عند اختيار نقل خدمات)
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal TransferFees { get; set; } = 0;

        /// <summary>
        /// هل يحتاج إلى تجديد يدوي (للمهني فقط)
        /// </summary>
        public bool RequiresManualRenewal { get; set; } = false;

        /// <summary>
        /// تاريخ إصدار رخصة العمل (للمهني فقط)
        /// </summary>
        public DateTime? WorkPermitIssueDate { get; set; }

        /// <summary>
        /// هل مؤهل لتجديد رخصة العمل (للمهني فقط)
        /// </summary>
        public bool IsEligibleForWorkPermitRenewal
        {
            get
            {
                if (!WorkPermitIssueDate.HasValue || WorkerType.ToString() != "مهني" || ServiceType.ToString() != "تجديد")
                    return true; // إذا لم يتم تحديد تاريخ أو ليس مهني أو ليس تجديد

                try
                {
                    var hijriCalendar = new UmAlQuraCalendar();
                    var currentHijriYear = hijriCalendar.GetYear(DateTime.Now);
                    var workPermitHijriYear = hijriCalendar.GetYear(WorkPermitIssueDate.Value);

                    // مؤهل فقط إذا تم إصدار رخصة العمل في سنة هجرية سابقة
                    return workPermitHijriYear < currentHijriYear;
                }
                catch
                {
                    return true; // في حالة الخطأ، نعتبره مؤهل
                }
            }
        }

        /// <summary>
        /// حالة الأهلية مع التفاصيل
        /// </summary>
        public string EligibilityStatus
        {
            get
            {
                if (!WorkPermitIssueDate.HasValue || WorkerType.ToString() != "مهني" || ServiceType.ToString() != "تجديد")
                    return ""; // لا يطبق

                try
                {
                    var hijriCalendar = new UmAlQuraCalendar();
                    var currentHijriYear = hijriCalendar.GetYear(DateTime.Now);
                    var workPermitHijriYear = hijriCalendar.GetYear(WorkPermitIssueDate.Value);

                    if (workPermitHijriYear < currentHijriYear)
                    {
                        return $"✅ مؤهل للتجديد - تم إصدار رخصة العمل في السنة الهجرية {workPermitHijriYear} هـ (السنة الحالية: {currentHijriYear} هـ)";
                    }
                    else if (workPermitHijriYear == currentHijriYear)
                    {
                        return $"❌ غير مؤهل للتجديد - تم إصدار رخصة العمل في نفس السنة الهجرية الحالية ({workPermitHijriYear} هـ)";
                    }
                    else
                    {
                        return $"❌ غير مؤهل للتجديد - تاريخ إصدار رخصة العمل في المستقبل ({workPermitHijriYear} هـ)";
                    }
                }
                catch
                {
                    return "⚠️ خطأ في تحديد حالة الأهلية";
                }
            }
        }

        /// <summary>
        /// رسالة عدم الأهلية المختصرة
        /// </summary>
        public string IneligibilityMessage
        {
            get
            {
                if (IsEligibleForWorkPermitRenewal)
                    return "";

                try
                {
                    var hijriCalendar = new UmAlQuraCalendar();
                    var currentHijriYear = hijriCalendar.GetYear(DateTime.Now);
                    var workPermitHijriYear = hijriCalendar.GetYear(WorkPermitIssueDate!.Value);

                    if (workPermitHijriYear == currentHijriYear)
                    {
                        return $"غير مؤهل للتجديد - تم إصدار رخصة العمل في نفس السنة الهجرية ({workPermitHijriYear} هـ)";
                    }
                    else if (workPermitHijriYear > currentHijriYear)
                    {
                        return $"غير مؤهل للتجديد - تاريخ إصدار رخصة العمل في المستقبل ({workPermitHijriYear} هـ)";
                    }
                    else
                    {
                        return "غير مؤهل للتجديد";
                    }
                }
                catch
                {
                    return "غير مؤهل للتجديد";
                }
            }
        }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // خصائص محسوبة للعرض
        public decimal TotalAmount => PassportFees + WorkPermitFees + MedicalInsurance + SaudiEmployment + LaborOfficeFees + ServiceFees + TransferFees;
        public string TotalAmountFormatted => $"{TotalAmount:N2} ريال";
        public string ExpiryDateFormatted => ExpiryDate.ToString("yyyy/MM/dd");
        public string CreatedDateFormatted => CreatedDate.ToString("yyyy/MM/dd HH:mm");

        // نوع العامل
        public string WorkerTypeText => WorkerType switch
        {
            WorkerType.Professional => "مهني",
            WorkerType.Domestic => "منزلي",
            _ => "غير محدد"
        };

        // نوع الخدمة
        public string ServiceTypeText => ServiceType switch
        {
            ServiceType.Renewal => "تجديد",
            ServiceType.Transfer => "نقل خدمات",
            _ => "غير محدد"
        };

        // نوع التجديد
        public string RenewalTypeText => RequiresManualRenewal ? "تجديد يدوي" : "تجديد تلقائي";

        // حالة انتهاء الإقامة
        public int DaysUntilExpiry
        {
            get
            {
                var days = (ExpiryDate - DateTime.Now).Days;
                return days > 0 ? days : 0;
            }
        }

        public bool IsExpiringSoon => DaysUntilExpiry <= 30;
        public bool IsExpired => ExpiryDate < DateTime.Now;

        public string ExpiryStatus
        {
            get
            {
                if (IsExpired)
                    return "منتهية الصلاحية";
                if (IsExpiringSoon)
                    return $"تنتهي خلال {DaysUntilExpiry} يوم";
                return $"صالحة لمدة {DaysUntilExpiry} يوم";
            }
        }

        // التواريخ الهجرية
        public string ExpiryDateHijri => ConvertToHijri(ExpiryDate);
        public string WorkPermitIssueDateHijri => WorkPermitIssueDate.HasValue ? ConvertToHijri(WorkPermitIssueDate.Value) : "غير محدد";
        public string WorkPermitIssueDateFormatted => WorkPermitIssueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";

        /// <summary>
        /// تحويل التاريخ الميلادي إلى هجري
        /// </summary>
        private string ConvertToHijri(DateTime gregorianDate)
        {
            try
            {
                var hijriCalendar = new UmAlQuraCalendar();
                var hijriYear = hijriCalendar.GetYear(gregorianDate);
                var hijriMonth = hijriCalendar.GetMonth(gregorianDate);
                var hijriDay = hijriCalendar.GetDayOfMonth(gregorianDate);
                
                return $"{hijriDay:00}/{hijriMonth:00}/{hijriYear}";
            }
            catch
            {
                return "غير متاح";
            }
        }

        /// <summary>
        /// تطبيق الرسوم الافتراضية حسب نوع العامل ونوع الخدمة
        /// </summary>
        public void ApplyDefaultFees()
        {
            // إعادة تعيين جميع الرسوم
            PassportFees = 0;
            WorkPermitFees = 0;
            MedicalInsurance = 0;
            SaudiEmployment = 0;
            LaborOfficeFees = 0;
            ServiceFees = 0;
            TransferFees = 0;

            if (ServiceType == ServiceType.Renewal) // تجديد
            {
                if (WorkerType == WorkerType.Professional) // مهني - تجديد
                {
                    PassportFees = 300;
                    WorkPermitFees = 2000;
                    MedicalInsurance = 600;
                    SaudiEmployment = 2400;
                    ServiceFees = 500;
                }
                else if (WorkerType == WorkerType.Domestic) // منزلي - تجديد
                {
                    PassportFees = 300;
                    LaborOfficeFees = 500;
                    ServiceFees = 300;
                }
            }
            else if (ServiceType == ServiceType.Transfer) // نقل خدمات
            {
                if (WorkerType == WorkerType.Professional) // مهني - نقل
                {
                    PassportFees = 300;
                    TransferFees = 1500;
                    ServiceFees = 800;
                }
                else if (WorkerType == WorkerType.Domestic) // منزلي - نقل
                {
                    PassportFees = 300;
                    LaborOfficeFees = 500;
                    TransferFees = 1000;
                    ServiceFees = 500;
                }
            }
        }

        /// <summary>
        /// استخراج تاريخ إصدار الإقامة من رقم الإقامة
        /// </summary>
        public DateTime? ExtractResidenceIssueDate()
        {
            try
            {
                if (string.IsNullOrEmpty(ResidenceNumber) || ResidenceNumber.Length < 10)
                    return null;

                // استخراج التاريخ من رقم الإقامة (الأرقام الأولى تمثل التاريخ)
                var dateString = ResidenceNumber.Substring(0, 6);

                if (dateString.Length == 6)
                {
                    var year = int.Parse("20" + dateString.Substring(0, 2));
                    var month = int.Parse(dateString.Substring(2, 2));
                    var day = int.Parse(dateString.Substring(4, 2));

                    if (year >= 2000 && year <= DateTime.Now.Year && month >= 1 && month <= 12 && day >= 1 && day <= 31)
                    {
                        return new DateTime(year, month, day);
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// حساب مدة الإقامة بالسنوات
        /// </summary>
        public int GetResidenceYears()
        {
            var issueDate = ExtractResidenceIssueDate();
            if (issueDate.HasValue)
            {
                return DateTime.Now.Year - issueDate.Value.Year;
            }
            return 0;
        }
    }

    /// <summary>
    /// نوع العامل
    /// </summary>
    public enum WorkerType
    {
        Professional = 1,  // مهني
        Domestic = 2       // منزلي
    }

    /// <summary>
    /// نوع الخدمة
    /// </summary>
    public enum ServiceType
    {
        Renewal = 1,   // تجديد
        Transfer = 2   // نقل خدمات
    }
}
