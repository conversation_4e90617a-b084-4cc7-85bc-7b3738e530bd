﻿#pragma checksum "..\..\..\WorkersWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "50621231E5FD51A4CA856FE00E1C3148C852CD83"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// WorkersWindow
    /// </summary>
    public partial class WorkersWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 141 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnShowData;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddWorker;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DataSection;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalWorkers;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtActiveWorkers;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpiredResidences;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpiringResidences;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEdit;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDelete;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridWorkers;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InputSection;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormTitle;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtWorkerName;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtResidenceNumber;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DpEntryDate;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DpSubscriptionEndDate;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSponsorName;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSponsorIdNumber;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.MemoEdit TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\WorkersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/workerswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\WorkersWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.BtnShowData = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\WorkersWindow.xaml"
            this.BtnShowData.Click += new System.Windows.RoutedEventHandler(this.BtnShowData_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAddWorker = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\WorkersWindow.xaml"
            this.BtnAddWorker.Click += new System.Windows.RoutedEventHandler(this.BtnAddWorker_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DataSection = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.TxtTotalWorkers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtActiveWorkers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtExpiredResidences = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtExpiringResidences = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtSearch = ((DevExpress.Xpf.Editors.TextEdit)(target));
            
            #line 253 "..\..\..\WorkersWindow.xaml"
            this.TxtSearch.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.TxtSearch_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnEdit = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\WorkersWindow.xaml"
            this.BtnEdit.Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnDelete = ((System.Windows.Controls.Button)(target));
            
            #line 261 "..\..\..\WorkersWindow.xaml"
            this.BtnDelete.Click += new System.Windows.RoutedEventHandler(this.BtnDelete_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 263 "..\..\..\WorkersWindow.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.GridWorkers = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 14:
            
            #line 291 "..\..\..\WorkersWindow.xaml"
            ((DevExpress.Xpf.Grid.TableView)(target)).FocusedRowChanged += new DevExpress.Xpf.Grid.FocusedRowChangedEventHandler(this.GridWorkers_FocusedRowChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.InputSection = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.FormTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtWorkerName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 18:
            this.TxtResidenceNumber = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 19:
            this.DpEntryDate = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 20:
            this.DpSubscriptionEndDate = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 21:
            this.TxtSponsorName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 22:
            this.TxtSponsorIdNumber = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 23:
            this.TxtNotes = ((DevExpress.Xpf.Editors.MemoEdit)(target));
            return;
            case 24:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\WorkersWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 394 "..\..\..\WorkersWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 396 "..\..\..\WorkersWindow.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

