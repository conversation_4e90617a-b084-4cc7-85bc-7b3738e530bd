using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArabicDashboard.Models
{
    public class Commitment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string CommitmentName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string CommitmentType { get; set; } = string.Empty;

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        [StringLength(50)]
        public string CommitmentCategory { get; set; } = "شخصي";

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "مستحق";

        public bool IsNotificationEnabled { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public DateTime? PaymentDate { get; set; }

        [StringLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        public bool IsActive { get; set; } = true;

        // خصائص محسوبة
        public int DaysRemaining
        {
            get
            {
                if (Status == "مسدد") return 0;
                var days = (DueDate.Date - DateTime.Today).Days;
                return days;
            }
        }

        public bool IsOverdue => DaysRemaining < 0 && Status != "مسدد";

        public bool IsNearDue => DaysRemaining <= 15 && DaysRemaining >= 0 && Status != "مسدد";

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "مسدد" => "#10B981",
                    _ when IsOverdue => "#EF4444",
                    _ when IsNearDue => "#F59E0B",
                    _ => "#6B7280"
                };
            }
        }

        public string StatusIcon
        {
            get
            {
                return Status switch
                {
                    "مسدد" => "✅",
                    _ when IsOverdue => "🔴",
                    _ when IsNearDue => "⚠️",
                    _ => "📅"
                };
            }
        }

        public string FormattedDueDate => DueDate.ToString("yyyy/MM/dd");

        public string FormattedAmount => Amount > 0 ? $"{Amount:N2} ريال" : "";

        public string DaysRemainingText
        {
            get
            {
                if (Status == "مسدد") return "مسدد";
                if (IsOverdue) return $"متأخر {Math.Abs(DaysRemaining)} يوم";
                if (DaysRemaining == 0) return "مستحق اليوم";
                return $"{DaysRemaining} يوم متبقي";
            }
        }
    }
}
